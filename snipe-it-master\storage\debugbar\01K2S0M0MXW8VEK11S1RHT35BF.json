{"__meta": {"id": "01K2S0M0MXW8VEK11S1RHT35BF", "datetime": "2025-08-16 08:40:57", "utime": **********.246036, "method": "GET", "uri": "/api/v1/hardware/checkins/overdue?search=&sort=expected_checkin&order=asc&offset=0&limit=20", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"count": 11, "start": **********.669659, "end": **********.246065, "duration": 0.5764060020446777, "duration_str": "576ms", "measures": [{"label": "Booting", "start": **********.669659, "relative_start": 0, "end": **********.047693, "relative_end": **********.047693, "duration": 0.****************, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.047708, "relative_start": 0.****************, "end": **********.246068, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "198ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.173961, "relative_start": 0.****************, "end": **********.177512, "relative_end": **********.177512, "duration": 0.003551006317138672, "duration_str": "3.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "select * from `users` where `id` = ? and `users`.`deleted_at` is null limit 1", "start": **********.195382, "relative_start": 0.****************, "end": **********.197472, "relative_end": **********.197472, "duration": 0.002089977264404297, "duration_str": "2.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select `permission_groups`.*, `users_groups`.`user_id` as `pivot_user_id`, `users_groups`.`group_id`...", "start": **********.2100792, "relative_start": 0.****************, "end": **********.211229, "relative_end": **********.211229, "duration": 0.001149892807006836, "duration_str": "1.15ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `custom_fields`", "start": **********.221081, "relative_start": 0.551422119140625, "end": **********.222271, "relative_end": **********.222271, "duration": 0.0011899471282958984, "duration_str": "1.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` inner join `status_labels` as `status_alias` on `status_a...", "start": **********.226801, "relative_start": 0.5571420192718506, "end": **********.228081, "relative_end": **********.228081, "duration": 0.001280069351196289, "duration_str": "1.28ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` inner join `status_labels` as `status_alias` on `status_a...", "start": **********.231671, "relative_start": 0.5620121955871582, "end": **********.232831, "relative_end": **********.232831, "duration": 0.0011599063873291016, "duration_str": "1.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select `assets`.* from `assets` inner join `status_labels` as `status_alias` on `status_alias`.`id`...", "start": **********.2347438, "relative_start": 0.5650849342346191, "end": **********.236044, "relative_end": **********.236044, "duration": 0.0013000965118408203, "duration_str": "1.3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "Preparing Response", "start": **********.239369, "relative_start": 0.5697100162506104, "end": **********.242362, "relative_end": **********.242362, "duration": 0.0029931068420410156, "duration_str": "2.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.243034, "relative_start": 0.5733749866485596, "end": **********.243067, "relative_end": **********.243067, "duration": 3.314018249511719e-05, "duration_str": "33μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.2.12", "Environment": "production", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "UTC", "Locale": "en-US"}}, "event": {"count": 32, "start": **********.669659, "end": **********.246158, "duration": 0.****************, "duration_str": "576ms", "measures": [{"label": "Illuminate\\Routing\\Events\\Routing", "start": **********.173967, "relative_start": 0.****************, "end": **********.173967, "relative_end": **********.173967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\Routing"}, {"label": "Illuminate\\Routing\\Events\\RouteMatched", "start": **********.177535, "relative_start": 0.****************, "end": **********.177535, "relative_end": **********.177535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\RouteMatched"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.196298, "relative_start": 0.****************, "end": **********.196298, "relative_end": **********.196298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.20074, "relative_start": 0.****************, "end": **********.20074, "relative_end": **********.20074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "eloquent.retrieved: App\\Models\\User", "start": **********.201252, "relative_start": 0.****************, "end": **********.201252, "relative_end": **********.201252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.retrieved"}, {"label": "Illuminate\\Foundation\\Events\\LocaleUpdated", "start": **********.204043, "relative_start": 0.53438401222229, "end": **********.204043, "relative_end": **********.204043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Foundation\\Events\\LocaleUpdated"}, {"label": "Illuminate\\Cache\\Events\\RetrievingKey", "start": **********.206175, "relative_start": 0.5365161895751953, "end": **********.206175, "relative_end": **********.206175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Cache\\Events\\RetrievingKey"}, {"label": "Illuminate\\Cache\\Events\\CacheHit", "start": **********.206963, "relative_start": 0.537304162979126, "end": **********.206963, "relative_end": **********.206963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Cache\\Events\\CacheHit"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.210591, "relative_start": 0.5409321784973145, "end": **********.210591, "relative_end": **********.210591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.215481, "relative_start": 0.5458221435546875, "end": **********.215481, "relative_end": **********.215481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.21956, "relative_start": 0.549901008605957, "end": **********.21956, "relative_end": **********.21956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "eloquent.booting: App\\Models\\CustomField", "start": **********.220711, "relative_start": 0.5510520935058594, "end": **********.220711, "relative_end": **********.220711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booting"}, {"label": "eloquent.booted: App\\Models\\CustomField", "start": **********.220836, "relative_start": 0.5511770248413086, "end": **********.220836, "relative_end": **********.220836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.221635, "relative_start": 0.551976203918457, "end": **********.221635, "relative_end": **********.221635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.224115, "relative_start": 0.5544559955596924, "end": **********.224115, "relative_end": **********.224115, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "eloquent.retrieved: App\\Models\\CustomField", "start": **********.224275, "relative_start": 0.5546162128448486, "end": **********.224275, "relative_end": **********.224275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.retrieved"}, {"label": "eloquent.booting: App\\Models\\Statuslabel", "start": **********.225083, "relative_start": 0.5554242134094238, "end": **********.225083, "relative_end": **********.225083, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booting"}, {"label": "eloquent.booted: App\\Models\\Statuslabel", "start": **********.225204, "relative_start": 0.5555450916290283, "end": **********.225204, "relative_end": **********.225204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.227305, "relative_start": 0.5576460361480713, "end": **********.227305, "relative_end": **********.227305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.231043, "relative_start": 0.5613842010498047, "end": **********.231043, "relative_end": **********.231043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.232171, "relative_start": 0.5625121593475342, "end": **********.232171, "relative_end": **********.232171, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.234417, "relative_start": 0.5647580623626709, "end": **********.234417, "relative_end": **********.234417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.235289, "relative_start": 0.5656301975250244, "end": **********.235289, "relative_end": **********.235289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.237575, "relative_start": 0.****************, "end": **********.237575, "relative_end": **********.237575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Routing\\Events\\PreparingResponse", "start": **********.239374, "relative_start": 0.****************, "end": **********.239374, "relative_end": **********.239374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\PreparingResponse"}, {"label": "Illuminate\\Routing\\Events\\ResponsePrepared", "start": **********.242378, "relative_start": 0.****************, "end": **********.242378, "relative_end": **********.242378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\ResponsePrepared"}, {"label": "Illuminate\\Cache\\Events\\RetrievingKey", "start": **********.242412, "relative_start": 0.****************, "end": **********.242412, "relative_end": **********.242412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Cache\\Events\\RetrievingKey"}, {"label": "Illuminate\\Cache\\Events\\CacheHit", "start": **********.242672, "relative_start": 0.****************, "end": **********.242672, "relative_end": **********.242672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Cache\\Events\\CacheHit"}, {"label": "Illuminate\\Cache\\Events\\RetrievingKey", "start": **********.242688, "relative_start": 0.****************, "end": **********.242688, "relative_end": **********.242688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Cache\\Events\\RetrievingKey"}, {"label": "Illuminate\\Cache\\Events\\CacheHit", "start": **********.242924, "relative_start": 0.****************, "end": **********.242924, "relative_end": **********.242924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Cache\\Events\\CacheHit"}, {"label": "Illuminate\\Routing\\Events\\PreparingResponse", "start": **********.243036, "relative_start": 0.****************, "end": **********.243036, "relative_end": **********.243036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\PreparingResponse"}, {"label": "Illuminate\\Routing\\Events\\ResponsePrepared", "start": **********.243073, "relative_start": 0.****************, "end": **********.243073, "relative_end": **********.243073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\ResponsePrepared"}], "nb_measures": 32}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/hardware/{action}/{upcoming_status}", "middleware": "auth:api, api, api-throttle:api", "controller": "App\\Http\\Controllers\\Api\\AssetsController@index<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FApi%2FAssetsController.php&line=58\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/v1/hardware", "as": "api.assets.list-upcoming", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FApi%2FAssetsController.php&line=58\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/AssetsController.php:58-450</a>"}, "queries": {"count": 6, "nb_statements": 6, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00817, "accumulated_duration_str": "8.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/passport/src/PassportUserProvider.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\PassportUserProvider.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Guards/TokenGuard.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php", "line": 257}, {"index": 19, "namespace": null, "name": "vendor/laravel/passport/src/Guards/TokenGuard.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php", "line": 122}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}], "start": **********.195382, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "snipeit", "explain": null}, {"sql": "select `permission_groups`.*, `users_groups`.`user_id` as `pivot_user_id`, `users_groups`.`group_id` as `pivot_group_id` from `permission_groups` inner join `users_groups` on `permission_groups`.`id` = `users_groups`.`group_id` where `users_groups`.`user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": [], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\User.php", "line": 204}, {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\User.php", "line": 269}, {"index": 22, "namespace": null, "name": "app/Providers/AuthServiceProvider.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Providers\\AuthServiceProvider.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 443}], "start": **********.2100792, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "User.php:204", "source": {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\User.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FModels%2FUser.php&line=204", "ajax": false, "filename": "User.php", "line": "204"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `custom_fields`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/AssetsController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\Api\\AssetsController.php", "line": 126}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.221081, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "AssetsController.php:126", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/AssetsController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\Api\\AssetsController.php", "line": 126}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FApi%2FAssetsController.php&line=126", "ajax": false, "filename": "AssetsController.php", "line": "126"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` inner join `status_labels` as `status_alias` on `status_alias`.`id` = `assets`.`status_id` and `status_alias`.`archived` = 0 where `assets`.`expected_checkin` is not null and `assets`.`expected_checkin` < '2025-08-16' and `assets`.`archived` = 0 and `assets`.`assigned_to` is not null and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0, "2025-08-16", 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/AssetsController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\Api\\AssetsController.php", "line": 433}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.226801, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "AssetsController.php:433", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/AssetsController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\Api\\AssetsController.php", "line": 433}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FApi%2FAssetsController.php&line=433", "ajax": false, "filename": "AssetsController.php", "line": "433"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` inner join `status_labels` as `status_alias` on `status_alias`.`id` = `assets`.`status_id` and `status_alias`.`archived` = 0 where `assets`.`expected_checkin` is not null and `assets`.`expected_checkin` < '2025-08-16' and `assets`.`archived` = 0 and `assets`.`assigned_to` is not null and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0, "2025-08-16", 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/AssetsController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\Api\\AssetsController.php", "line": 436}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.231671, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "AssetsController.php:436", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/AssetsController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\Api\\AssetsController.php", "line": 436}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FApi%2FAssetsController.php&line=436", "ajax": false, "filename": "AssetsController.php", "line": "436"}, "connection": "snipeit", "explain": null}, {"sql": "select `assets`.* from `assets` inner join `status_labels` as `status_alias` on `status_alias`.`id` = `assets`.`status_id` and `status_alias`.`archived` = 0 where `assets`.`expected_checkin` is not null and `assets`.`expected_checkin` < '2025-08-16' and `assets`.`archived` = 0 and `assets`.`assigned_to` is not null and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null order by `expected_checkin` asc limit 20 offset 0", "type": "query", "params": [], "bindings": [0, "2025-08-16", 0, 0], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/AssetsController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\Api\\AssetsController.php", "line": 437}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.2347438, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "AssetsController.php:437", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/AssetsController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\Api\\AssetsController.php", "line": 437}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FApi%2FAssetsController.php&line=437", "ajax": false, "filename": "AssetsController.php", "line": "437"}, "connection": "snipeit", "explain": null}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\CustomField": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FModels%2FCustomField.php&line=1", "ajax": false, "filename": "CustomField.php", "line": "?"}}}, "count": 2, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 2}}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "logs": {"count": 0, "messages": []}, "files": {"messages": [{"message": "'\\server.php',", "is_string": true}, {"message": "'\\public\\index.php',", "is_string": true}, {"message": "'\\vendor\\autoload.php',", "is_string": true}, {"message": "'\\vendor\\composer\\autoload_real.php',", "is_string": true}, {"message": "'\\vendor\\composer\\platform_check.php',", "is_string": true}, {"message": "'\\vendor\\composer\\ClassLoader.php',", "is_string": true}, {"message": "'\\vendor\\composer\\autoload_static.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\deprecation-contracts\\function.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-mbstring\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-mbstring\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-normalizer\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-normalizer\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-ctype\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-ctype\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-grapheme\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-grapheme\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\string\\Resources\\functions.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-php83\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-php83\\bootstrap81.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Resources\\functions\\dump.php',", "is_string": true}, {"message": "'\\vendor\\ralouphie\\getallheaders\\src\\getallheaders.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-php80\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-idn\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-idn\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\react\\promise\\src\\functions_include.php',", "is_string": true}, {"message": "'\\vendor\\react\\promise\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\functions_include.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\prompts\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\termwind\\src\\Functions.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\clock\\Resources\\now.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-uuid\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-uuid\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Resources\\functions.php',", "is_string": true}, {"message": "'\\vendor\\ramsey\\uuid\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\fakerphp\\faker\\src\\Faker\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\mtdowling\\jmespath.php\\src\\JmesPath.php',", "is_string": true}, {"message": "'\\vendor\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock\\autoload.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\php-text-template\\src\\Template.php',", "is_string": true}, {"message": "'\\vendor\\phpseclib\\phpseclib\\phpseclib\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\flare-client-php\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-php81\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\aws\\aws-sdk-php\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\ezyang\\htmlpurifier\\library\\HTMLPurifier.composer.php',", "is_string": true}, {"message": "'\\vendor\\phpstan\\phpstan\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php',", "is_string": true}, {"message": "'\\vendor\\psy\\psysh\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\helpers\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\league\\csv\\src\\functions_include.php',", "is_string": true}, {"message": "'\\vendor\\league\\csv\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\mockery\\mockery\\library\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\mockery\\mockery\\library\\Mockery.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\collision\\src\\Adapters\\Phpunit\\Autoload.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Runner\\Version.php',", "is_string": true}, {"message": "'\\vendor\\sebastian\\version\\src\\Version.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\collision\\src\\Adapters\\Phpunit\\Subscribers\\EnsurePrinterIsRegisteredSubscriber.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Event\\Events\\Application\\StartedSubscriber.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Event\\Subscriber.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\autoload.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\autoload-php7.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\src\\Compat.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\namespaced.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\sodium_compat.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\constants.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\php84compat_const.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\php84compat.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\stream-xchacha20.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\ristretto255.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock-phpunit\\autoload.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Rule\\InvocationOrder.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\SelfDescribing.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Invocation.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Interface\\MockObject.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Interface\\Stub.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\InvocationMocker.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\InvocationStubber.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\MethodNameMatch.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\ParametersMatch.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\Stub.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\Identity.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Rule\\MethodName.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\InvocationHandler.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock-phpunit\\classes\\MockDisablerPHPUnit10.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Event\\Events\\Test\\Lifecycle\\FinishedSubscriber.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock-phpunit\\classes\\DefaultArgumentRemoverReturnTypes100.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock-phpunit\\classes\\MockObjectProxyReturnTypes100.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-backup\\src\\Helpers\\functions.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\helpers.php',", "is_string": true}, {"message": "'\\bootstrap\\app.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Container\\Container.php',", "is_string": true}, {"message": "'\\vendor\\psr\\container\\src\\ContainerInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Foundation\\Application.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Foundation\\CachesConfiguration.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Foundation\\CachesRoutes.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-kernel\\HttpKernelInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\EventServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\Context\\ContextServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RoutingServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Http\\Kernel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\InteractsWithTime.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Http\\Kernel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Container\\ContextualAttribute.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Tappable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Routing\\BindingRegistrar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Routing\\Registrar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ReflectsClosures.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Events\\Dispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollectionInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Request.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\Request.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\AcceptHeader.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\AcceptHeaderItem.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\FileBag.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\ParameterBag.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\HeaderBag.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\HeaderUtils.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\InputBag.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\ServerBag.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Concerns\\CanBePrecognitive.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Concerns\\InteractsWithContentTypes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Concerns\\InteractsWithFlashData.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Concerns\\InteractsWithInput.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Dumpable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\InteractsWithData.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Conditionable\\Traits\\Conditionable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Arrayable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Carbon.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Carbon.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Date.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Boundaries.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Comparison.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Converter.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\ToStringFormat.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Creator.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\ObjectInitialisation.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\LocalFactory.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Difference.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Macro.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Mixin.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\MagicParameter.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Modifiers.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Mutability.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Cast.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Options.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\StaticOptions.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Localization.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\StaticLocalization.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Rounding.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\IntervalRounding.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Serialization.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Test.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Timestamp.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Units.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Week.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonTimeZone.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonInterval.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\IntervalStep.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonConverterInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\FactoryImmutable.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\clock\\ClockInterface.php',", "is_string": true}, {"message": "'\\vendor\\psr\\clock\\src\\ClockInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonImmutable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadEnvironmentVariables.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Env.php',", "is_string": true}, {"message": "'\\vendor\\phpoption\\phpoption\\src\\PhpOption\\Option.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\RepositoryBuilder.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\ServerConstAdapter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\AdapterInterface.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\ReaderInterface.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\WriterInterface.php',", "is_string": true}, {"message": "'\\vendor\\phpoption\\phpoption\\src\\PhpOption\\Some.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\EnvConstAdapter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\PutenvAdapter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\MultiReader.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\MultiWriter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\ImmutableWriter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\AdapterRepository.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\RepositoryInterface.php',", "is_string": true}, {"message": "'\\vendor\\phpoption\\phpoption\\src\\PhpOption\\None.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php',", "is_string": true}, {"message": "'\\bootstrap\\cache\\config.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Config\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Config\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\EnvironmentDetector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\AliasLoader.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\CanBeEscapedWhenCastToString.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Enumerable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Jsonable.php',", "is_string": true}, {"message": "'\\bootstrap\\cache\\packages.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php',", "is_string": true}, {"message": "'\\bootstrap\\cache\\services.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\CookieServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasEvents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasGlobalScopes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasRelationships.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasTimestamps.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasUniqueIds.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HidesAttributes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\PreventsCircularRecursion.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\HasCollection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Broadcasting\\HasBroadcastChannel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Queue\\QueueableEntity.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Routing\\UrlRoutable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\SerializableClosure.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Signers\\Hmac.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Contracts\\Signer.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Serializers\\Signed.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Contracts\\Serializable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FoundationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\AggregateServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Testing\\ParallelTestingServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Testing\\Concerns\\TestDatabases.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\DeferrableProvider.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\AbstractCloner.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\ClonerInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\HtmlDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Dumper\\HtmlDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Dumper\\CliDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Dumper\\AbstractDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Dumper\\DataDumperInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\DumperInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Concerns\\ResolvesDumpSource.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\VarCloner.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\HigherOrderTapProxy.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Caster\\ReflectionCaster.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\VarDumper.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Uri.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Htmlable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Responsable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Reflector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\PaginationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\PaginationState.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\Paginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Pagination\\Paginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\CursorPaginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractCursorPaginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Pagination\\CursorPaginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\arietimmerman\\laravel-scim-server\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-dompdf\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\eduardokum\\laravel-mail-auto-embed\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\intervention\\image\\src\\Intervention\\Image\\ImageServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\intervention\\image\\src\\Intervention\\Image\\ImageServiceProviderLaravelRecent.php',", "is_string": true}, {"message": "'\\vendor\\laravel-notification-channels\\google-chat\\src\\GoogleChatServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel-notification-channels\\microsoft-teams\\src\\MicrosoftTeamsServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Notification.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Manager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Notifications\\Dispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Notifications\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\Passport.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\ContextualBindingBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Container\\ContextualBindingBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Auth.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\slack-notification-channel\\src\\SlackChannelServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\ui\\src\\UiServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\LivewireServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\LivewireManager.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\EventBus.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\PersistentMiddleware\\PersistentMiddleware.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\Mechanism.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\FrontendAssets\\FrontendAssets.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendBlade.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\CompileLivewireTags\\CompileLivewireTags.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\RenderComponent.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\DataStore.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Laravel\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\collision\\src\\Adapters\\Laravel\\CollisionServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\phpinsights\\src\\Application\\Adapters\\Laravel\\InsightsServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\phpinsights\\src\\Application\\Injectors\\Repositories.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\termwind\\src\\Laravel\\TermwindServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\osa-eg\\laravel-teams-notification\\src\\LaravelTeamsNotificationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\pragmarx\\google2fa-laravel\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-backup\\src\\BackupServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessAssets.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessBladeComponents.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessCommands.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessConfigs.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessInertia.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessMigrations.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessRoutes.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessServiceProviders.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessTranslations.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessViewComposers.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessViews.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessViewSharedData.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Package.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasAssets.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasBladeComponents.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasCommands.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasConfigs.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasInertia.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasInstallCommand.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasMigrations.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasRoutes.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasServiceProviders.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasTranslations.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasViewComposers.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasViews.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasViewSharedData.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\IgnitionServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\ignition\\src\\Config\\IgnitionConfig.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\ignition\\src\\Config\\FileConfigManager.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\ignition\\src\\Contracts\\ConfigManager.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\error-solutions\\src\\SolutionProviderRepository.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\error-solutions\\src\\Contracts\\SolutionProviderRepository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Log.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\ParsesLogConfiguration.php',", "is_string": true}, {"message": "'\\vendor\\psr\\log\\src\\LoggerInterface.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Level.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-signal-aware-command\\src\\SignalAwareCommandServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\BreadcrumbsServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Route.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CreatesRegularExpressionRouteConstraints.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\FiltersControllerMiddleware.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php',", "is_string": true}, {"message": "'\\vendor\\unicodeveloper\\laravel-password\\src\\DumbPasswordServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\AppServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\AuthServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\AuthServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\EventServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\RouteServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\SettingsServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\ValidationServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\BladeServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\LivewireServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\MacroServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\SamlServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\BreadcrumbsServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\ConnectionResolverInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Renderer\\Listener.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteBinding.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\debugbar-routes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteAction.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteUri.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteGroup.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\console\\Application.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\service-contracts\\ResetInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Console\\Application.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Event.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\routes\\web.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\ui\\src\\AuthRouteMethods.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\AboutCommand.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\console\\Command\\Command.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\console\\Command\\SignalableCommandInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\ConfiguresPrompts.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\HasParameters.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\InteractsWithIO.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\InteractsWithSignals.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\PromptsForMissingInput.php',", "is_string": true}, {"message": "'\\vendor\\composer\\InstalledVersions.php',", "is_string": true}, {"message": "'\\vendor\\composer\\installed.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\console\\Output\\OutputInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Stringable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Blade.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Compiler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesAuthorizations.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesClasses.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesComments.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesComponents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesConditionals.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesEchos.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesErrors.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesFragments.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesHelpers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesIncludes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesInjections.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesJson.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesJs.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesLayouts.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesLoops.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesRawPhp.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesSessions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesStacks.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesStyles.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesTranslations.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesUseStatements.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\CompilerInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\EngineResolver.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\CompileLivewireTags\\LivewireTagPrecompiler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\ComponentHookRegistry.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportWireModelingNestedComponents\\SupportWireModelingNestedComponents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\ComponentHook.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportMultipleRootElementDetection\\SupportMultipleRootElementDetection.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\SupportDisablingBackButtonCache.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportNestedComponentListeners\\SupportNestedComponentListeners.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportMorphAwareIfStatement\\SupportMorphAwareIfStatement.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Livewire.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportAutoInjectedAssets\\SupportAutoInjectedAssets.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportComputed\\SupportLegacyComputedPropertySyntax.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportNestingComponents\\SupportNestingComponents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportScriptsAndAssets\\SupportScriptsAndAssets.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportBladeAttributes\\SupportBladeAttributes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ComponentAttributeBag.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportConsoleCommands\\SupportConsoleCommands.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\View\\View.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Renderable.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportReactiveProps\\SupportReactiveProps.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportFileDownloads\\SupportFileDownloads.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportJsEvaluation\\SupportJsEvaluation.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportQueryString\\SupportQueryString.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportFileUploads\\SupportFileUploads.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportTeleporting\\SupportTeleporting.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportLazyLoading\\SupportLazyLoading.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportFormObjects\\SupportFormObjects.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportAttributes\\SupportAttributes.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination\\SupportPagination.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportValidation\\SupportValidation.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportIsolating\\SupportIsolating.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportRedirects\\SupportRedirects.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportStreaming\\SupportStreaming.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportNavigate\\SupportNavigate.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Vite.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportEntangle\\SupportEntangle.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportLocales\\SupportLocales.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportTesting\\SupportTesting.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\SupportModels.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportEvents\\SupportEvents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportLegacyModels\\SupportLegacyModels.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportWireables\\SupportWireables.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Translator.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation-contracts\\TranslatorInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Translator.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\MessageCatalogue.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\MessageCatalogueInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\MetadataAwareInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\CatalogueMetadataAwareInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\TranslatorBagInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation-contracts\\LocaleAwareInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\lazy\\Carbon\\TranslatorStrongType.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\AbstractTranslator.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\TranslatorStrongTypeInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Loader\\ArrayLoader.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Loader\\LoaderInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\MessageFormatter\\MessageFormatterMapper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Formatter\\MessageFormatterInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\lazy\\Carbon\\MessageFormatter\\MessageFormatterMapperStrongType.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Formatter\\MessageFormatter.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Formatter\\IntlFormatter.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Formatter\\IntlFormatterInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\IdentityTranslator.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation-contracts\\TranslatorTrait.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Lang\\en.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Lang\\en_US.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonPeriod.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\lazy\\Carbon\\UnprotectedDatePeriod.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Date.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\DateFactory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Events\\LocaleUpdated.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-backup\\src\\Notifications\\EventHandler.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-backup\\src\\Listeners\\EncryptBackupArchive.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\ignition-routes.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Http\\Controllers\\HealthCheckController.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Http\\Controllers\\ExecuteSolutionController.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Validation\\ValidatesRequests.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Http\\Controllers\\UpdateConfigController.php',", "is_string": true}, {"message": "'\\app\\Exceptions\\Handler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Debug\\ExceptionHandler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\ReportableHandler.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\DumpRecorder\\DumpRecorder.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\DumpRecorder\\MultiDumpHandler.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\JobRecorder\\JobRecorder.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\LogRecorder\\LogRecorder.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\QueryRecorder\\QueryRecorder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\QueueServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Serializers\\Native.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\QueueManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Queue\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Queue\\Monitor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Validator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Validation\\Factory.php',", "is_string": true}, {"message": "'\\app\\Providers\\SnipeTranslationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Translation\\TranslationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Translation\\FileLoader.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Translation\\Loader.php',", "is_string": true}, {"message": "'\\app\\Services\\SnipeTranslator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Translation\\Translator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\NamespacedItemResolver.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Translation\\Translator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifierInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\PresenceVerifierInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Routing\\UrlGenerator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Optional.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\NullHandler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\Handler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\HandlerInterface.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Logger.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\ResettableInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\Logger.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\LogRecord.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\JsonSerializableDateTimeImmutable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\Context\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\Events\\MessageLogged.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\LogRecorder\\LogMessage.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\URL.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Schema.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ConfigurationUrlParser.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DetectsConcurrencyErrors.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DetectsLostConnections.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\ConnectionInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Grammars\\MySqlGrammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Grammars\\Grammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Grammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\CompilesJsonPaths.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseTransactionsManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Events\\ConnectionEstablished.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Events\\ConnectionEvent.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Grammars\\Grammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php',", "is_string": true}, {"message": "'\\app\\Models\\Asset.php',", "is_string": true}, {"message": "'\\app\\Models\\Depreciable.php',", "is_string": true}, {"message": "'\\app\\Models\\SnipeModel.php',", "is_string": true}, {"message": "'\\app\\Models\\CompanyableTrait.php',", "is_string": true}, {"message": "'\\app\\Models\\Traits\\HasUploads.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Factories\\HasFactory.php',", "is_string": true}, {"message": "'\\app\\Models\\Loggable.php',", "is_string": true}, {"message": "'\\app\\Models\\Requestable.php',", "is_string": true}, {"message": "'\\app\\Presenters\\Presentable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\SoftDeletes.php',", "is_string": true}, {"message": "'\\vendor\\watson\\validating\\src\\ValidatingTrait.php',", "is_string": true}, {"message": "'\\vendor\\watson\\validating\\src\\Injectors\\UniqueInjector.php',", "is_string": true}, {"message": "'\\app\\Http\\Traits\\UniqueUndeletedTrait.php',", "is_string": true}, {"message": "'\\app\\Models\\Traits\\Acceptable.php',", "is_string": true}, {"message": "'\\app\\Models\\Traits\\Searchable.php',", "is_string": true}, {"message": "'\\app\\Models\\CompanyableScope.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Scope.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\SoftDeletingScope.php',", "is_string": true}, {"message": "'\\vendor\\watson\\validating\\src\\ValidatingObserver.php',", "is_string": true}, {"message": "'\\app\\Observers\\AssetObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\User.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Authenticatable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Auth\\Access\\Authorizable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Passwords\\CanResetPassword.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\HasApiTokens.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Notifiable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\HasDatabaseNotifications.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\RoutesNotifications.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Authenticatable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Access\\Authorizable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\CanResetPassword.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Translation\\HasLocalePreference.php',", "is_string": true}, {"message": "'\\app\\Observers\\UserObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\Accessory.php',", "is_string": true}, {"message": "'\\app\\Observers\\AccessoryObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\Component.php',", "is_string": true}, {"message": "'\\app\\Observers\\ComponentObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\Consumable.php',", "is_string": true}, {"message": "'\\app\\Observers\\ConsumableObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\License.php',", "is_string": true}, {"message": "'\\app\\Observers\\LicenseObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\Setting.php',", "is_string": true}, {"message": "'\\app\\Observers\\SettingObserver.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Gate.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\HandlesAuthorization.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Access\\Gate.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Unit.php',", "is_string": true}, {"message": "'\\app\\Listeners\\LogListener.php',", "is_string": true}, {"message": "'\\app\\Listeners\\CheckoutableListener.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\RateLimiter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cache\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\FileStore.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RetrievesMultipleKeys.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cache\\Store.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cache\\LockProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cache\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\psr\\simple-cache\\src\\CacheInterface.php',", "is_string": true}, {"message": "'\\routes\\api.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResourceRegistrar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\PendingResourceRegistration.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Pluralizer.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\InflectorFactory.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Language.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\English\\InflectorFactory.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\GenericLanguageInflectorFactory.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\LanguageInflectorFactory.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\English\\Rules.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Ruleset.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Transformations.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\WordInflector.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\English\\Inflectible.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Transformation.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Pattern.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Patterns.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\English\\Uninflected.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Substitutions.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Substitution.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Word.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Inflector.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\CachedWordInflector.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\RulesetInflector.php',", "is_string": true}, {"message": "'\\routes\\web\\hardware.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Support\\ClosureScope.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Support\\ReflectionClosure.php',", "is_string": true}, {"message": "'\\routes\\web\\models.php',", "is_string": true}, {"message": "'\\routes\\web\\accessories.php',", "is_string": true}, {"message": "'\\routes\\web\\licenses.php',", "is_string": true}, {"message": "'\\routes\\web\\locations.php',", "is_string": true}, {"message": "'\\routes\\web\\consumables.php',", "is_string": true}, {"message": "'\\routes\\web\\fields.php',", "is_string": true}, {"message": "'\\routes\\web\\components.php',", "is_string": true}, {"message": "'\\routes\\web\\users.php',", "is_string": true}, {"message": "'\\routes\\web\\kits.php',", "is_string": true}, {"message": "'\\routes\\web.php',", "is_string": true}, {"message": "'\\app\\Livewire\\Importer.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Component.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Auth\\Access\\AuthorizesRequests.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Concerns\\InteractsWithProperties.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportEvents\\HandlesEvents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportRedirects\\HandlesRedirects.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportStreaming\\HandlesStreaming.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportAttributes\\HandlesAttributes.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportValidation\\HandlesValidation.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportFormObjects\\HandlesFormObjects.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportJsEvaluation\\HandlesJsEvaluation.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\HandlesDisablingBackButtonCache.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\HealthController.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\DashboardController.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\Controller.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bus\\DispatchesJobs.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\GroupsController.php',", "is_string": true}, {"message": "'\\routes\\scim.php',", "is_string": true}, {"message": "'\\vendor\\arietimmerman\\laravel-scim-server\\src\\RouteProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewFinderInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesFragments.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesLayouts.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesLoops.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesStacks.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesTranslations.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\View\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewName.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\App.php',", "is_string": true}, {"message": "'\\resources\\macros\\macros.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\FormFacade.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\HtmlServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\FormBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\Componentable.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\HtmlBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\FileSessionHandler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Session\\Session.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Breadcrumbs.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Manager.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Trail.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Registrar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Pipeline\\Pipeline.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\TrustProxies.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\NoSessionStore.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\Concerns\\ExcludesPaths.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\MaintenanceModeManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\FileBasedMaintenanceMode.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Foundation\\MaintenanceMode.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SymfonySessionDecorator.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\Session\\SessionInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ViewErrorBag.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckForSetup.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsWhereDateClauses.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ExplainsQueries.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Database\\Query\\Builder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Database\\Eloquent\\Builder.php',", "is_string": true}, {"message": "'\\app\\Models\\Company.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectorInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Events\\StatementPrepared.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Events\\QueryExecuted.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\QueryRecorder\\Query.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Collection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\InteractsWithDictionary.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Queue\\QueueableCollection.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckForDebug.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\CreatesUserProviders.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\StatefulGuard.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Guard.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\SupportsBasicAuth.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\UserProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Hashing\\HashServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Hashing\\HashManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Hashing\\Hasher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Timebox.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\CookieJar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cookie\\QueueingFactory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cookie\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Events\\Authenticated.php',", "is_string": true}, {"message": "'\\app\\Models\\Group.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\InteractsWithPivotTable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\JoinClause.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\TrimStrings.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\SecurityHeaders.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\PreventBackHistory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php',", "is_string": true}, {"message": "'\\vendor\\fruitcake\\php-cors\\src\\CorsService.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\IpUtils.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\LaravelDebugbar.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DebugBar.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\Support\\RequestIdGenerator.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\RequestIdGeneratorInterface.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\Storage\\FilesystemStorage.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\Storage\\StorageInterface.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\PhpInfoCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\DataCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\HasDataFormatter.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\HasXdebugLinks.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\DataCollectorInterface.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\Renderable.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\MessagesCollector.php',", "is_string": true}, {"message": "'\\vendor\\psr\\log\\src\\AbstractLogger.php',", "is_string": true}, {"message": "'\\vendor\\psr\\log\\src\\LoggerTrait.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\MessagesAggregateInterface.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\AssetProvider.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\TimeDataCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\DataFormatter.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\DataFormatterInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\error-handler\\ErrorRenderer\\FileLinkFormatter.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\error-handler\\ErrorRenderer\\ErrorRendererInterface.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\MemoryCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\ExceptionsCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\LaravelCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\EventCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataFormatter\\SimpleFormatter.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\ViewCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\RouteCollector.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\StreamHandler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\AbstractProcessingHandler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\AbstractHandler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\ProcessableHandlerTrait.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\FormattableHandlerTrait.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\ProcessableHandlerInterface.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\FormattableHandlerInterface.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Utils.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Formatter\\LineFormatter.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Formatter\\NormalizerFormatter.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Formatter\\FormatterInterface.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\QueryCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\PDO\\PDOCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataFormatter\\QueryFormatter.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\ObjectCountCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\LivewireCollector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\MailServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\Bridge\\Symfony\\SymfonyMailCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\LogsCollector.php',", "is_string": true}, {"message": "'\\vendor\\psr\\log\\src\\LogLevel.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\FilesCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\MultiAuthCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\GateCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\JavascriptRenderer.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\JavascriptRenderer.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Events\\Routing.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\routing\\Route.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\routing\\RouteCompiler.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\routing\\RouteCompilerInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\routing\\CompiledRoute.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\UriValidator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\ValidatorInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\MethodValidator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\SchemeValidator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\HostValidator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteParameterBinder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Events\\RouteMatched.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\Api\\AssetsController.php',", "is_string": true}, {"message": "'\\app\\Http\\Traits\\MigratesLegacyAssetLocations.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Contracts\\ControllerDispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\MiddlewareNameResolver.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\SortedMiddleware.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Middleware\\AuthenticatesRequests.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckLocale.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\SetAPIResponseHeaders.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php',", "is_string": true}, {"message": "'\\vendor\\league\\oauth2-server\\src\\ResourceServer.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\Bridge\\AccessTokenRepository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\Bridge\\FormatsScopesForStorage.php',", "is_string": true}, {"message": "'\\vendor\\league\\oauth2-server\\src\\Repositories\\AccessTokenRepositoryInterface.php',", "is_string": true}, {"message": "'\\vendor\\league\\oauth2-server\\src\\Repositories\\RepositoryInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\TokenRepository.php',", "is_string": true}, {"message": "'\\vendor\\league\\oauth2-server\\src\\CryptKey.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\PassportUserProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\ClientRepository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\Encrypter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Encryption\\Encrypter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Encryption\\StringEncrypter.php',", "is_string": true}, {"message": "'\\vendor\\firebase\\php-jwt\\src\\JWT.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\CookieValuePrefix.php',", "is_string": true}, {"message": "'\\vendor\\firebase\\php-jwt\\src\\Key.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\TransientToken.php',", "is_string": true}, {"message": "'\\app\\Helpers\\Helper.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiting\\Limit.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Events\\RetrievingKey.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Events\\CacheEvent.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Events\\CacheHit.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\LockableFile.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteSignatureParameters.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\DebugBarVarDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\Data.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\Stub.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\VarDumper\\DebugBarHtmlDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\Cursor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Events\\GateEvaluated.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Response.php',", "is_string": true}, {"message": "'\\app\\Models\\CustomField.php',", "is_string": true}, {"message": "'\\app\\Models\\Statuslabel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\BelongsTo.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\ComparesRelatedModels.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\SupportsDefaultModels.php',", "is_string": true}, {"message": "'\\app\\Http\\Transformers\\AssetsTransformer.php',", "is_string": true}, {"message": "'\\app\\Http\\Transformers\\DatatablesTransformer.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Events\\PreparingResponse.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\JsonResponse.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\Response.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\ResponseHeaderBag.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\ResponseTrait.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Events\\ResponsePrepared.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\SymfonyHttpDriver.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\HttpDriverInterface.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\SessionCollector.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\uid\\Ulid.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\uid\\AbstractUid.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\uid\\HashableInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\uid\\TimeBasedUidInterface.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\RequestCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\Support\\Clockwork\\ClockworkCollector.php',", "is_string": true}], "count": 946}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"mgpiattos\"\n  \"user\" => array:43 [\n    \"id\" => 1\n    \"email\" => \"<EMAIL>\"\n    \"activated\" => 1\n    \"created_by\" => null\n    \"activated_at\" => null\n    \"last_login\" => \"2025-08-16 08:39:49\"\n    \"first_name\" => \"<PERSON>\"\n    \"last_name\" => \"Piattos\"\n    \"created_at\" => \"2025-08-16T08:39:28.000000Z\"\n    \"updated_at\" => \"2025-08-16T08:39:49.000000Z\"\n    \"deleted_at\" => null\n    \"website\" => null\n    \"country\" => null\n    \"gravatar\" => null\n    \"location_id\" => null\n    \"phone\" => null\n    \"jobtitle\" => null\n    \"manager_id\" => null\n    \"employee_num\" => null\n    \"avatar\" => null\n    \"username\" => \"mgpiattos\"\n    \"notes\" => null\n    \"company_id\" => null\n    \"ldap_import\" => 0\n    \"locale\" => \"en-US\"\n    \"show_in_list\" => 1\n    \"two_factor_enrolled\" => 0\n    \"two_factor_optin\" => 0\n    \"department_id\" => null\n    \"address\" => null\n    \"city\" => null\n    \"state\" => null\n    \"zip\" => null\n    \"skin\" => null\n    \"remote\" => 0\n    \"start_date\" => null\n    \"end_date\" => null\n    \"scim_externalid\" => null\n    \"autoassign_licenses\" => 1\n    \"vip\" => 0\n    \"enable_sounds\" => 0\n    \"enable_confetti\" => 0\n    \"groups\" => []\n  ]\n]", "api": "array:2 [\n  \"name\" => \"mgpiattos\"\n  \"user\" => array:43 [\n    \"id\" => 1\n    \"email\" => \"<EMAIL>\"\n    \"activated\" => 1\n    \"created_by\" => null\n    \"activated_at\" => null\n    \"last_login\" => \"2025-08-16 08:39:49\"\n    \"first_name\" => \"<PERSON>\"\n    \"last_name\" => \"Piattos\"\n    \"created_at\" => \"2025-08-16T08:39:28.000000Z\"\n    \"updated_at\" => \"2025-08-16T08:39:49.000000Z\"\n    \"deleted_at\" => null\n    \"website\" => null\n    \"country\" => null\n    \"gravatar\" => null\n    \"location_id\" => null\n    \"phone\" => null\n    \"jobtitle\" => null\n    \"manager_id\" => null\n    \"employee_num\" => null\n    \"avatar\" => null\n    \"username\" => \"mgpiattos\"\n    \"notes\" => null\n    \"company_id\" => null\n    \"ldap_import\" => 0\n    \"locale\" => \"en-US\"\n    \"show_in_list\" => 1\n    \"two_factor_enrolled\" => 0\n    \"two_factor_optin\" => 0\n    \"department_id\" => null\n    \"address\" => null\n    \"city\" => null\n    \"state\" => null\n    \"zip\" => null\n    \"skin\" => null\n    \"remote\" => 0\n    \"start_date\" => null\n    \"end_date\" => null\n    \"scim_externalid\" => null\n    \"autoassign_licenses\" => 1\n    \"vip\" => 0\n    \"enable_sounds\" => 0\n    \"enable_confetti\" => 0\n    \"groups\" => []\n  ]\n]"}, "names": "web: mgpiattos, api: mgpiattos"}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => index,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>index App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.219069, "xdebug_link": null}]}, "session": {"url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/_debugbar/assets/javascript?v=1753138890\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_token": "XcFb6uBj6sHVSCD8PzSZfpkew7wBekXM9V2bT1eX", "backUrl": "http://127.0.0.1:8000/login", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "$2y$10$2Y6DCYiWr0S5QSGbJatyfuYNCX1.Ib9MeBtmJ22fHJVGhcCwvfQ3G", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/api/v1/hardware/checkins/overdue?limit=20&offset=0&order=asc&search=&sort=expe...", "action_name": "api.assets.list-upcoming", "controller_action": "App\\Http\\Controllers\\Api\\AssetsController@index", "uri": "GET api/v1/hardware/{action}/{upcoming_status}", "controller": "App\\Http\\Controllers\\Api\\AssetsController@index<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FApi%2FAssetsController.php&line=58\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/v1/hardware", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FApi%2FAssetsController.php&line=58\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/AssetsController.php:58-450</a>", "middleware": "auth:api, api, api-throttle:api", "duration": "583ms", "peak_memory": "36MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-673796087 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"16 characters\">expected_checkin</span>\"\n  \"<span class=sf-dump-key>order</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n  \"<span class=sf-dump-key>offset</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-673796087\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-341804996 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-341804996\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1592958099 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">XcFb6uBj6sHVSCD8PzSZfpkew7wBekXM9V2bT1eX</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Microsoft Edge&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1078 characters\">csrftoken=eRZeQ4hlaHT8dltJRqLktclcixjXegkX; snipeit_session=Q20TsmwTLpe7V4NLPLzu7UJvMbqL6bJ6wfpUffVy; snipeit_passport_token=eyJpdiI6Ik9MZG1ieXYrY2FkWDBuMFp4TG9uaEE9PSIsInZhbHVlIjoic3I5TVhJRkJrWXd6OGZSZ3ZlQllXN3lnS21RT2tpby9KdC81Y0RtZWtMajJETGlQNUJhM3FId1JDNHpnM0hHY0g4WHdDQ2lBSkswSjdHZWpUMDRuUGFmREhnSmZ2V3ZockpSY1BITWltNHIrYnZ4QUVTVlh2ZGdlc2Y4MFQ0bnIvVDV6QUMxaHRSblYyeTBvUHdzaEVkRXI2ZlJ0TUNhR0Zja25ySzhDWFRna2E4eDN5M0VjdnJXODZWR3drQ0tUR3lwSHdvU3hvVXdJaVE1SUhIdTEzWXQ2aTlUL3lJd3Y4eCs3aTU2aHpoeHc4ejU5WWh3cWlvaHRtOTFYOEhtRDBOSDU2NS9wSHdPRWFmWEVqUCthL01nQ2lhWkFJdGhYQStGVDJaOXkxUnpHZWZEb0l6REZLazVsNjRDMEtFK0EiLCJtYWMiOiJkMzM0ZGMwZTgxOWNhM2FjNzE1OWJiOTkxN2ZkZjE4NTRjYTExYWZhYWQ4NDg2MTU2OGY3YmViYjVjYTE4MzMzIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InhJZ3VFb0M4dXcyaC9YQUVGeThUVnc9PSIsInZhbHVlIjoiMnZJcGRRajJ3OFBNMThCeFdaVlVnSHdmZ0toMnJTeVlhZFhGWFNHc1gvajhnTDVueEJzRTk4T3NKQzNmbU1wYjh3Z28vVXIvT1dWMXFqZ1JFS2pJalY1RnYrV1IydEZ3eVg5bS9uZEN5dWZRek1pRUZxUUs3T0l6SFNZdkFDekMiLCJtYWMiOiI2YjIzYTY5OWI3MjYyNTk1MmRmMjE0YjUxMDEzMGNlOGIwYWE4MDBlODBiYTQwMzE0MTBlN2EyMzIyOWVjN2M5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1592958099\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2116375338 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>csrftoken</span>\" => \"<span class=sf-dump-str title=\"32 characters\">eRZeQ4hlaHT8dltJRqLktclcixjXegkX</span>\"\n  \"<span class=sf-dump-key>snipeit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Q20TsmwTLpe7V4NLPLzu7UJvMbqL6bJ6wfpUffVy</span>\"\n  \"<span class=sf-dump-key>snipeit_passport_token</span>\" => \"<span class=sf-dump-str title=\"596 characters\">eyJpdiI6Ik9MZG1ieXYrY2FkWDBuMFp4TG9uaEE9PSIsInZhbHVlIjoic3I5TVhJRkJrWXd6OGZSZ3ZlQllXN3lnS21RT2tpby9KdC81Y0RtZWtMajJETGlQNUJhM3FId1JDNHpnM0hHY0g4WHdDQ2lBSkswSjdHZWpUMDRuUGFmREhnSmZ2V3ZockpSY1BITWltNHIrYnZ4QUVTVlh2ZGdlc2Y4MFQ0bnIvVDV6QUMxaHRSblYyeTBvUHdzaEVkRXI2ZlJ0TUNhR0Zja25ySzhDWFRna2E4eDN5M0VjdnJXODZWR3drQ0tUR3lwSHdvU3hvVXdJaVE1SUhIdTEzWXQ2aTlUL3lJd3Y4eCs3aTU2aHpoeHc4ejU5WWh3cWlvaHRtOTFYOEhtRDBOSDU2NS9wSHdPRWFmWEVqUCthL01nQ2lhWkFJdGhYQStGVDJaOXkxUnpHZWZEb0l6REZLazVsNjRDMEtFK0EiLCJtYWMiOiJkMzM0ZGMwZTgxOWNhM2FjNzE1OWJiOTkxN2ZkZjE4NTRjYTExYWZhYWQ4NDg2MTU2OGY3YmViYjVjYTE4MzMzIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InhJZ3VFb0M4dXcyaC9YQUVGeThUVnc9PSIsInZhbHVlIjoiMnZJcGRRajJ3OFBNMThCeFdaVlVnSHdmZ0toMnJTeVlhZFhGWFNHc1gvajhnTDVueEJzRTk4T3NKQzNmbU1wYjh3Z28vVXIvT1dWMXFqZ1JFS2pJalY1RnYrV1IydEZ3eVg5bS9uZEN5dWZRek1pRUZxUUs3T0l6SFNZdkFDekMiLCJtYWMiOiI2YjIzYTY5OWI3MjYyNTk1MmRmMjE0YjUxMDEzMGNlOGIwYWE4MDBlODBiYTQwMzE0MTBlN2EyMzIyOWVjN2M5IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2116375338\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1888529992 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 16 Aug 2025 08:40:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">120</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">118</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>retry-after</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">31</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-reset</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">31</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-reset-timestamp</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">1755333688</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-clockwork-id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">01K2S0M0MXW8VEK11S1RHT35BF</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-clockwork-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-clockwork-path</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">_debugbar/clockwork/</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1888529992\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1148217690 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"62 characters\">http://127.0.0.1:8000/_debugbar/assets/javascript?v=1753138890</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XcFb6uBj6sHVSCD8PzSZfpkew7wBekXM9V2bT1eX</span>\"\n  \"<span class=sf-dump-key>backUrl</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$2Y6DCYiWr0S5QSGbJatyfuYNCX1.Ib9MeBtmJ22fHJVGhcCwvfQ3G</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1148217690\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/api/v1/hardware/checkins/overdue?limit=20&offset=0&order=asc&search=&sort=expe...", "action_name": "api.assets.list-upcoming", "controller_action": "App\\Http\\Controllers\\Api\\AssetsController@index"}, "badge": null}, "clockwork": {"getData": {"search": null, "sort": "expected_checkin", "order": "asc", "offset": "0", "limit": "20"}, "postData": [], "headers": {"host": ["127.0.0.1:8000"], "connection": ["keep-alive"], "sec-ch-ua-platform": ["\"Windows\""], "x-csrf-token": ["XcFb6uBj6sHVSCD8PzSZfpkew7wBekXM9V2bT1eX"], "sec-ch-ua": ["\"Not;A=Brand\";v=\"99\", \"Microsoft Edge\";v=\"139\", \"Chromium\";v=\"139\""], "sec-ch-ua-mobile": ["?0"], "x-requested-with": ["XMLHttpRequest"], "user-agent": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"], "accept": ["application/json, text/javascript, */*; q=0.01"], "content-type": ["application/json"], "sec-fetch-site": ["same-origin"], "sec-fetch-mode": ["cors"], "sec-fetch-dest": ["empty"], "referer": ["http://127.0.0.1:8000/"], "accept-encoding": ["gzip, deflate, br, zstd"], "accept-language": ["en-US,en;q=0.9"], "cookie": ["csrftoken=eRZeQ4hlaHT8dltJRqLktclcixjXegkX; snipeit_session=Q20TsmwTLpe7V4NLPLzu7UJvMbqL6bJ6wfpUffVy; snipeit_passport_token=eyJpdiI6Ik9MZG1ieXYrY2FkWDBuMFp4TG9uaEE9PSIsInZhbHVlIjoic3I5TVhJRkJrWXd6OGZSZ3ZlQllXN3lnS21RT2tpby9KdC81Y0RtZWtMajJETGlQNUJhM3FId1JDNHpnM0hHY0g4WHdDQ2lBSkswSjdHZWpUMDRuUGFmREhnSmZ2V3ZockpSY1BITWltNHIrYnZ4QUVTVlh2ZGdlc2Y4MFQ0bnIvVDV6QUMxaHRSblYyeTBvUHdzaEVkRXI2ZlJ0TUNhR0Zja25ySzhDWFRna2E4eDN5M0VjdnJXODZWR3drQ0tUR3lwSHdvU3hvVXdJaVE1SUhIdTEzWXQ2aTlUL3lJd3Y4eCs3aTU2aHpoeHc4ejU5WWh3cWlvaHRtOTFYOEhtRDBOSDU2NS9wSHdPRWFmWEVqUCthL01nQ2lhWkFJdGhYQStGVDJaOXkxUnpHZWZEb0l6REZLazVsNjRDMEtFK0EiLCJtYWMiOiJkMzM0ZGMwZTgxOWNhM2FjNzE1OWJiOTkxN2ZkZjE4NTRjYTExYWZhYWQ4NDg2MTU2OGY3YmViYjVjYTE4MzMzIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InhJZ3VFb0M4dXcyaC9YQUVGeThUVnc9PSIsInZhbHVlIjoiMnZJcGRRajJ3OFBNMThCeFdaVlVnSHdmZ0toMnJTeVlhZFhGWFNHc1gvajhnTDVueEJzRTk4T3NKQzNmbU1wYjh3Z28vVXIvT1dWMXFqZ1JFS2pJalY1RnYrV1IydEZ3eVg5bS9uZEN5dWZRek1pRUZxUUs3T0l6SFNZdkFDekMiLCJtYWMiOiI2YjIzYTY5OWI3MjYyNTk1MmRmMjE0YjUxMDEzMGNlOGIwYWE4MDBlODBiYTQwMzE0MTBlN2EyMzIyOWVjN2M5IiwidGFnIjoiIn0%3D"]}, "cookies": {"csrftoken": "eRZeQ4hlaHT8dltJRqLktclcixjXegkX", "snipeit_session": "Q20TsmwTLpe7V4NLPLzu7UJvMbqL6bJ6wfpUffVy", "snipeit_passport_token": "eyJpdiI6Ik9MZG1ieXYrY2FkWDBuMFp4TG9uaEE9PSIsInZhbHVlIjoic3I5TVhJRkJrWXd6OGZSZ3ZlQllXN3lnS21RT2tpby9KdC81Y0RtZWtMajJETGlQNUJhM3FId1JDNHpnM0hHY0g4WHdDQ2lBSkswSjdHZWpUMDRuUGFmREhnSmZ2V3ZockpSY1BITWltNHIrYnZ4QUVTVlh2ZGdlc2Y4MFQ0bnIvVDV6QUMxaHRSblYyeTBvUHdzaEVkRXI2ZlJ0TUNhR0Zja25ySzhDWFRna2E4eDN5M0VjdnJXODZWR3drQ0tUR3lwSHdvU3hvVXdJaVE1SUhIdTEzWXQ2aTlUL3lJd3Y4eCs3aTU2aHpoeHc4ejU5WWh3cWlvaHRtOTFYOEhtRDBOSDU2NS9wSHdPRWFmWEVqUCthL01nQ2lhWkFJdGhYQStGVDJaOXkxUnpHZWZEb0l6REZLazVsNjRDMEtFK0EiLCJtYWMiOiJkMzM0ZGMwZTgxOWNhM2FjNzE1OWJiOTkxN2ZkZjE4NTRjYTExYWZhYWQ4NDg2MTU2OGY3YmViYjVjYTE4MzMzIiwidGFnIjoiIn0=", "XSRF-TOKEN": "eyJpdiI6InhJZ3VFb0M4dXcyaC9YQUVGeThUVnc9PSIsInZhbHVlIjoiMnZJcGRRajJ3OFBNMThCeFdaVlVnSHdmZ0toMnJTeVlhZFhGWFNHc1gvajhnTDVueEJzRTk4T3NKQzNmbU1wYjh3Z28vVXIvT1dWMXFqZ1JFS2pJalY1RnYrV1IydEZ3eVg5bS9uZEN5dWZRek1pRUZxUUs3T0l6SFNZdkFDekMiLCJtYWMiOiI2YjIzYTY5OWI3MjYyNTk1MmRmMjE0YjUxMDEzMGNlOGIwYWE4MDBlODBiYTQwMzE0MTBlN2EyMzIyOWVjN2M5IiwidGFnIjoiIn0="}, "uri": "/api/v1/hardware/checkins/overdue?search=&sort=expected_checkin&order=asc&offset=0&limit=20", "method": "GET", "responseStatus": 200, "sessionData": {"url": [], "_previous": {"url": "http://127.0.0.1:8000/_debugbar/assets/javascript?v=1753138890"}, "_flash": {"old": [], "new": []}, "_token": "XcFb6uBj6sHVSCD8PzSZfpkew7wBekXM9V2bT1eX", "backUrl": "http://127.0.0.1:8000/login", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": 1, "password_hash_web": "$2y$10$2Y6DCYiWr0S5QSGbJatyfuYNCX1.Ib9MeBtmJ22fHJVGhcCwvfQ3G", "PHPDEBUGBAR_STACK_DATA": []}}}