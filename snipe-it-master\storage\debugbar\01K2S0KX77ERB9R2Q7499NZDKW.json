{"__meta": {"id": "01K2S0KX77ERB9R2Q7499NZDKW", "datetime": "2025-08-16 08:40:53", "utime": **********.736836, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[08:40:53] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 311", "message_html": null, "is_string": false, "label": "warning", "time": **********.67751, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 34, "start": **********.80861, "end": **********.736886, "duration": 0.9282760620117188, "duration_str": "928ms", "measures": [{"label": "Booting", "start": **********.80861, "relative_start": 0, "end": **********.338015, "relative_end": **********.338015, "duration": 0.****************, "duration_str": "529ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.338115, "relative_start": 0.****************, "end": **********.736891, "relative_end": 5.0067901611328125e-06, "duration": 0.****************, "duration_str": "399ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.472491, "relative_start": 0.****************, "end": **********.490824, "relative_end": **********.490824, "duration": 0.018332958221435547, "duration_str": "18.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "select count(*) as aggregate from `assets` where `assets`.`deleted_at` is null", "start": **********.506331, "relative_start": 0.***************, "end": **********.507691, "relative_end": **********.507691, "duration": 0.0013599395751953125, "duration_str": "1.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets...", "start": **********.5136828, "relative_start": 0.****************, "end": **********.514813, "relative_end": **********.514813, "duration": 0.0011301040649414062, "duration_str": "1.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`assigned_to` is null and exists (select *...", "start": **********.517089, "relative_start": 0.7084789276123047, "end": **********.517989, "relative_end": **********.517989, "duration": 0.0009000301361083984, "duration_str": "900μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assigned_to` > ? and `assets`.`deleted_at` is null", "start": **********.5203521, "relative_start": 0.7117421627044678, "end": **********.521222, "relative_end": **********.521222, "duration": 0.0008699893951416016, "duration_str": "870μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets...", "start": **********.523598, "relative_start": 0.7149879932403564, "end": **********.524628, "relative_end": **********.524628, "duration": 0.00102996826171875, "duration_str": "1.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets...", "start": **********.5269308, "relative_start": 0.7183208465576172, "end": **********.528061, "relative_end": **********.528061, "duration": 0.0011301040649414062, "duration_str": "1.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets...", "start": **********.5320148, "relative_start": 0.7234048843383789, "end": **********.532755, "relative_end": **********.532755, "duration": 0.00074005126953125, "duration_str": "740μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `byod` = ? and `assets`.`deleted_at` is null", "start": **********.5345, "relative_start": 0.7258899211883545, "end": **********.53503, "relative_end": **********.53503, "duration": 0.0005300045013427734, "duration_str": "530μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`next_audit_date` is not null and `assets`...", "start": **********.537153, "relative_start": 0.7285430431365967, "end": **********.538173, "relative_end": **********.538173, "duration": 0.0010199546813964844, "duration_str": "1.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`next_audit_date` is not null and `assets`...", "start": **********.540993, "relative_start": 0.7323830127716064, "end": **********.542013, "relative_end": **********.542013, "duration": 0.0010199546813964844, "duration_str": "1.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`expected_checkin` is not null and `assets...", "start": **********.544231, "relative_start": 0.7356209754943848, "end": **********.545011, "relative_end": **********.545011, "duration": 0.0007801055908203125, "duration_str": "780μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`expected_checkin` is not null and `assets...", "start": **********.547893, "relative_start": 0.7392830848693848, "end": **********.548583, "relative_end": **********.548583, "duration": 0.0006899833679199219, "duration_str": "690μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`deleted_at` is null", "start": **********.555512, "relative_start": 0.7469019889831543, "end": **********.556452, "relative_end": **********.556452, "duration": 0.0009400844573974609, "duration_str": "940μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `accessories` where `accessories`.`deleted_at` is null", "start": **********.558648, "relative_start": 0.7500381469726562, "end": **********.559108, "relative_end": **********.559108, "duration": 0.00045990943908691406, "duration_str": "460μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `license_seats` where `deleted_at` is null and `license_seats`.`de...", "start": **********.5621948, "relative_start": 0.7535848617553711, "end": **********.562795, "relative_end": **********.562795, "duration": 0.0006000995635986328, "duration_str": "600μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `consumables` where `consumables`.`deleted_at` is null", "start": **********.5646138, "relative_start": 0.7560038566589355, "end": **********.565024, "relative_end": **********.565024, "duration": 0.0004100799560546875, "duration_str": "410μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `components` where `components`.`deleted_at` is null", "start": **********.566537, "relative_start": 0.7579269409179688, "end": **********.567097, "relative_end": **********.567097, "duration": 0.0005600452423095703, "duration_str": "560μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `users` where `users`.`deleted_at` is null", "start": **********.570221, "relative_start": 0.7616109848022461, "end": **********.570791, "relative_end": **********.570791, "duration": 0.0005700588226318359, "duration_str": "570μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "Preparing Response", "start": **********.576032, "relative_start": 0.7674219608306885, "end": **********.732181, "relative_end": **********.732181, "duration": 0.15614914894104004, "duration_str": "156ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets...", "start": **********.5843961, "relative_start": 0.7757861614227295, "end": **********.585586, "relative_end": **********.585586, "duration": 0.0011899471282958984, "duration_str": "1.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`expected_checkin` is not null and `assets...", "start": **********.5966759, "relative_start": 0.7880659103393555, "end": **********.597766, "relative_end": **********.597766, "duration": 0.0010900497436523438, "duration_str": "1.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `next_audit_date` < ? and `next_audit_date` is not...", "start": **********.6004312, "relative_start": 0.7918212413787842, "end": **********.601231, "relative_end": **********.601231, "duration": 0.0007998943328857422, "duration_str": "800μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `asset_eol_date` is not null and `asset_eol_date` <...", "start": **********.604703, "relative_start": 0.7960929870605469, "end": **********.605643, "relative_end": **********.605643, "duration": 0.0009400844573974609, "duration_str": "940μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `warranty_months` is not null and DATE_ADD(purchase...", "start": **********.609914, "relative_start": 0.8013041019439697, "end": **********.610774, "relative_end": **********.610774, "duration": 0.0008599758148193359, "duration_str": "860μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `consumables` where `qty` <= `min_amt` and `min_amt` > ? and `cons...", "start": **********.613267, "relative_start": 0.804656982421875, "end": **********.613977, "relative_end": **********.613977, "duration": 0.0007100105285644531, "duration_str": "710μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select `consumables`.*, (select count(*) from `consumables_users` where `consumables`.`id` = `consum...", "start": **********.660952, "relative_start": 0.8523421287536621, "end": **********.661792, "relative_end": **********.661792, "duration": 0.0008399486541748047, "duration_str": "840μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select `accessories`.*, (select count(*) from `accessories_checkout` where `accessories`.`id` = `acc...", "start": **********.664323, "relative_start": 0.8557131290435791, "end": **********.665123, "relative_end": **********.665123, "duration": 0.0007998943328857422, "duration_str": "800μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `components` where `min_amt` is not null and `components`.`deleted_at` is null", "start": **********.6673908, "relative_start": 0.8587808609008789, "end": **********.667991, "relative_end": **********.667991, "duration": 0.0006000995635986328, "duration_str": "600μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `models` where `min_amt` > ? and `models`.`deleted_at` is null", "start": **********.670519, "relative_start": 0.8619091510772705, "end": **********.671019, "relative_end": **********.671019, "duration": 0.0004999637603759766, "duration_str": "500μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `licenses` where `min_amt` > ? and `licenses`.`deleted_at` is null", "start": **********.6744711, "relative_start": 0.865861177444458, "end": **********.675631, "relative_end": **********.675631, "duration": 0.0011599063873291016, "duration_str": "1.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select `status_labels`.*, (select count(*) from `assets` where `status_labels`.`id` = `assets`.`stat...", "start": **********.705239, "relative_start": 0.8966290950775146, "end": **********.706679, "relative_end": **********.706679, "duration": 0.0014400482177734375, "duration_str": "1.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.2.12", "Environment": "production", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "UTC", "Locale": "en-US"}}, "event": {"count": 320, "start": **********.80861, "end": **********.737071, "duration": 0.****************, "duration_str": "928ms", "measures": [{"label": "Illuminate\\Routing\\Events\\Routing", "start": **********.472566, "relative_start": 0.****************, "end": **********.472566, "relative_end": **********.472566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\Routing"}, {"label": "Illuminate\\Routing\\Events\\RouteMatched", "start": **********.490845, "relative_start": 0.****************, "end": **********.490845, "relative_end": **********.490845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\RouteMatched"}, {"label": "Illuminate\\Foundation\\Events\\LocaleUpdated", "start": **********.504047, "relative_start": 0.***************, "end": **********.504047, "relative_end": **********.504047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Foundation\\Events\\LocaleUpdated"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.506977, "relative_start": 0.****************, "end": **********.506977, "relative_end": **********.506977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.51093, "relative_start": 0.****************, "end": **********.51093, "relative_end": **********.51093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "eloquent.booting: App\\Models\\Statuslabel", "start": **********.511646, "relative_start": 0.7030360698699951, "end": **********.511646, "relative_end": **********.511646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booting"}, {"label": "eloquent.booted: App\\Models\\Statuslabel", "start": **********.511785, "relative_start": 0.7031750679016113, "end": **********.511785, "relative_end": **********.511785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.514175, "relative_start": 0.7055649757385254, "end": **********.514175, "relative_end": **********.514175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.516595, "relative_start": 0.7079849243164062, "end": **********.516595, "relative_end": **********.516595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.517349, "relative_start": 0.7087390422821045, "end": **********.517349, "relative_end": **********.517349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.520059, "relative_start": 0.711449146270752, "end": **********.520059, "relative_end": **********.520059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.520544, "relative_start": 0.7119340896606445, "end": **********.520544, "relative_end": **********.520544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.523047, "relative_start": 0.7144370079040527, "end": **********.523047, "relative_end": **********.523047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.524046, "relative_start": 0.7154359817504883, "end": **********.524046, "relative_end": **********.524046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.526331, "relative_start": 0.7177209854125977, "end": **********.526331, "relative_end": **********.526331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.527224, "relative_start": 0.7186141014099121, "end": **********.527224, "relative_end": **********.527224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.531275, "relative_start": 0.7226650714874268, "end": **********.531275, "relative_end": **********.531275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.532289, "relative_start": 0.7236790657043457, "end": **********.532289, "relative_end": **********.532289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.534238, "relative_start": 0.725628137588501, "end": **********.534238, "relative_end": **********.534238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.534649, "relative_start": 0.726038932800293, "end": **********.534649, "relative_end": **********.534649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.536495, "relative_start": 0.7278850078582764, "end": **********.536495, "relative_end": **********.536495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.537371, "relative_start": 0.7287609577178955, "end": **********.537371, "relative_end": **********.537371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.540351, "relative_start": 0.7317409515380859, "end": **********.540351, "relative_end": **********.540351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.541473, "relative_start": 0.7328629493713379, "end": **********.541473, "relative_end": **********.541473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.54361, "relative_start": 0.7350001335144043, "end": **********.54361, "relative_end": **********.54361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.544489, "relative_start": 0.7358789443969727, "end": **********.544489, "relative_end": **********.544489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.547057, "relative_start": 0.7384469509124756, "end": **********.547057, "relative_end": **********.547057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.548158, "relative_start": 0.7395479679107666, "end": **********.548158, "relative_end": **********.548158, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.550071, "relative_start": 0.7414610385894775, "end": **********.550071, "relative_end": **********.550071, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.556009, "relative_start": 0.747399091720581, "end": **********.556009, "relative_end": **********.556009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.558422, "relative_start": 0.749812126159668, "end": **********.558422, "relative_end": **********.558422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.558808, "relative_start": 0.7501981258392334, "end": **********.558808, "relative_end": **********.558808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.560367, "relative_start": 0.7517571449279785, "end": **********.560367, "relative_end": **********.560367, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "eloquent.booting: App\\Models\\LicenseSeat", "start": **********.561295, "relative_start": 0.7526850700378418, "end": **********.561295, "relative_end": **********.561295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booting"}, {"label": "eloquent.booted: App\\Models\\LicenseSeat", "start": **********.561957, "relative_start": 0.7533469200134277, "end": **********.561957, "relative_end": **********.561957, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.562423, "relative_start": 0.7538130283355713, "end": **********.562423, "relative_end": **********.562423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.564362, "relative_start": 0.7557520866394043, "end": **********.564362, "relative_end": **********.564362, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.56475, "relative_start": 0.7561399936676025, "end": **********.56475, "relative_end": **********.56475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.566284, "relative_start": 0.757673978805542, "end": **********.566284, "relative_end": **********.566284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.566691, "relative_start": 0.7580809593200684, "end": **********.566691, "relative_end": **********.566691, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.569928, "relative_start": 0.7613179683685303, "end": **********.569928, "relative_end": **********.569928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.570425, "relative_start": 0.761815071105957, "end": **********.570425, "relative_end": **********.570425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.573377, "relative_start": 0.7647669315338135, "end": **********.573377, "relative_end": **********.573377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "creating: dashboard", "start": **********.575666, "relative_start": 0.****************, "end": **********.575666, "relative_end": **********.575666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "Illuminate\\Routing\\Events\\PreparingResponse", "start": **********.576036, "relative_start": 0.****************, "end": **********.576036, "relative_end": **********.576036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\PreparingResponse"}, {"label": "composing: dashboard", "start": **********.579655, "relative_start": 0.****************, "end": **********.579655, "relative_end": **********.579655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.584795, "relative_start": 0.****************, "end": **********.584795, "relative_end": **********.584795, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.588321, "relative_start": 0.****************, "end": **********.588321, "relative_end": **********.588321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.590928, "relative_start": 0.782318115234375, "end": **********.590928, "relative_end": **********.590928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.590961, "relative_start": 0.782351016998291, "end": **********.590961, "relative_end": **********.590961, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.591931, "relative_start": 0.7833211421966553, "end": **********.591931, "relative_end": **********.591931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.591947, "relative_start": 0.7833371162414551, "end": **********.591947, "relative_end": **********.591947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.592381, "relative_start": 0.7837710380554199, "end": **********.592381, "relative_end": **********.592381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.592395, "relative_start": 0.7837851047515869, "end": **********.592395, "relative_end": **********.592395, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.592723, "relative_start": 0.7841129302978516, "end": **********.592723, "relative_end": **********.592723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.592735, "relative_start": 0.7841250896453857, "end": **********.592735, "relative_end": **********.592735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.593338, "relative_start": 0.7847280502319336, "end": **********.593338, "relative_end": **********.593338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.593351, "relative_start": 0.7847409248352051, "end": **********.593351, "relative_end": **********.593351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.593685, "relative_start": 0.7850749492645264, "end": **********.593685, "relative_end": **********.593685, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.593696, "relative_start": 0.7850861549377441, "end": **********.593696, "relative_end": **********.593696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.594215, "relative_start": 0.7856049537658691, "end": **********.594215, "relative_end": **********.594215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.594228, "relative_start": 0.7856180667877197, "end": **********.594228, "relative_end": **********.594228, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.594548, "relative_start": 0.785938024520874, "end": **********.594548, "relative_end": **********.594548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.594559, "relative_start": 0.7859489917755127, "end": **********.594559, "relative_end": **********.594559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.594895, "relative_start": 0.7862849235534668, "end": **********.594895, "relative_end": **********.594895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.594906, "relative_start": 0.7862961292266846, "end": **********.594906, "relative_end": **********.594906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.595205, "relative_start": 0.7865951061248779, "end": **********.595205, "relative_end": **********.595205, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.595215, "relative_start": 0.7866051197052002, "end": **********.595215, "relative_end": **********.595215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.595546, "relative_start": 0.7869360446929932, "end": **********.595546, "relative_end": **********.595546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.595556, "relative_start": 0.7869460582733154, "end": **********.595556, "relative_end": **********.595556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.595868, "relative_start": 0.7872581481933594, "end": **********.595868, "relative_end": **********.595868, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.595878, "relative_start": 0.7872679233551025, "end": **********.595878, "relative_end": **********.595878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.597041, "relative_start": 0.78843092918396, "end": **********.597041, "relative_end": **********.597041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.599329, "relative_start": 0.7907190322875977, "end": **********.599329, "relative_end": **********.599329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.599414, "relative_start": 0.7908041477203369, "end": **********.599414, "relative_end": **********.599414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.599426, "relative_start": 0.790816068649292, "end": **********.599426, "relative_end": **********.599426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.599804, "relative_start": 0.791193962097168, "end": **********.599804, "relative_end": **********.599804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.599816, "relative_start": 0.7912061214447021, "end": **********.599816, "relative_end": **********.599816, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.600628, "relative_start": 0.792017936706543, "end": **********.600628, "relative_end": **********.600628, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.603353, "relative_start": 0.7947430610656738, "end": **********.603353, "relative_end": **********.603353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.603479, "relative_start": 0.7948689460754395, "end": **********.603479, "relative_end": **********.603479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.603493, "relative_start": 0.7948830127716064, "end": **********.603493, "relative_end": **********.603493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.603925, "relative_start": 0.7953150272369385, "end": **********.603925, "relative_end": **********.603925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.603937, "relative_start": 0.7953269481658936, "end": **********.603937, "relative_end": **********.603937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.604991, "relative_start": 0.7963809967041016, "end": **********.604991, "relative_end": **********.604991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.608353, "relative_start": 0.7997429370880127, "end": **********.608353, "relative_end": **********.608353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.608461, "relative_start": 0.7998509407043457, "end": **********.608461, "relative_end": **********.608461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.608474, "relative_start": 0.7998640537261963, "end": **********.608474, "relative_end": **********.608474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.608972, "relative_start": 0.8003621101379395, "end": **********.608972, "relative_end": **********.608972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.608998, "relative_start": 0.8003880977630615, "end": **********.608998, "relative_end": **********.608998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.610234, "relative_start": 0.801624059677124, "end": **********.610234, "relative_end": **********.610234, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.612187, "relative_start": 0.8035769462585449, "end": **********.612187, "relative_end": **********.612187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.612267, "relative_start": 0.803657054901123, "end": **********.612267, "relative_end": **********.612267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.612278, "relative_start": 0.8036680221557617, "end": **********.612278, "relative_end": **********.612278, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.612678, "relative_start": 0.8040680885314941, "end": **********.612678, "relative_end": **********.612678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.61269, "relative_start": 0.8040800094604492, "end": **********.61269, "relative_end": **********.61269, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.613486, "relative_start": 0.8048760890960693, "end": **********.613486, "relative_end": **********.613486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.616101, "relative_start": 0.8074910640716553, "end": **********.616101, "relative_end": **********.616101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.616224, "relative_start": 0.8076140880584717, "end": **********.616224, "relative_end": **********.616224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.616242, "relative_start": 0.8076319694519043, "end": **********.616242, "relative_end": **********.616242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.616975, "relative_start": 0.8083651065826416, "end": **********.616975, "relative_end": **********.616975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.616988, "relative_start": 0.8083779811859131, "end": **********.616988, "relative_end": **********.616988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.621819, "relative_start": 0.813209056854248, "end": **********.621819, "relative_end": **********.621819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.622616, "relative_start": 0.8140060901641846, "end": **********.622616, "relative_end": **********.622616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.622931, "relative_start": 0.8143210411071777, "end": **********.622931, "relative_end": **********.622931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.623259, "relative_start": 0.8146491050720215, "end": **********.623259, "relative_end": **********.623259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.623511, "relative_start": 0.8149011135101318, "end": **********.623511, "relative_end": **********.623511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.62374, "relative_start": 0.8151299953460693, "end": **********.62374, "relative_end": **********.62374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: partials.bootstrap-table", "start": **********.624214, "relative_start": 0.8156039714813232, "end": **********.624214, "relative_end": **********.624214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: partials.bootstrap-table", "start": **********.624248, "relative_start": 0.8156380653381348, "end": **********.624248, "relative_end": **********.624248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.627806, "relative_start": 0.8191959857940674, "end": **********.627806, "relative_end": **********.627806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.627826, "relative_start": 0.8192160129547119, "end": **********.627826, "relative_end": **********.627826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.629159, "relative_start": 0.8205490112304688, "end": **********.629159, "relative_end": **********.629159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.629176, "relative_start": 0.820565938949585, "end": **********.629176, "relative_end": **********.629176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.629613, "relative_start": 0.8210029602050781, "end": **********.629613, "relative_end": **********.629613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.629627, "relative_start": 0.8210170269012451, "end": **********.629627, "relative_end": **********.629627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.62998, "relative_start": 0.8213701248168945, "end": **********.62998, "relative_end": **********.62998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.629993, "relative_start": 0.821382999420166, "end": **********.629993, "relative_end": **********.629993, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.630342, "relative_start": 0.8217320442199707, "end": **********.630342, "relative_end": **********.630342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.630355, "relative_start": 0.8217449188232422, "end": **********.630355, "relative_end": **********.630355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.630671, "relative_start": 0.8220610618591309, "end": **********.630671, "relative_end": **********.630671, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.630683, "relative_start": 0.8220729827880859, "end": **********.630683, "relative_end": **********.630683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.630997, "relative_start": 0.8223869800567627, "end": **********.630997, "relative_end": **********.630997, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.631009, "relative_start": 0.8223991394042969, "end": **********.631009, "relative_end": **********.631009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.631321, "relative_start": 0.8227109909057617, "end": **********.631321, "relative_end": **********.631321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.631333, "relative_start": 0.8227231502532959, "end": **********.631333, "relative_end": **********.631333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.631674, "relative_start": 0.8230640888214111, "end": **********.631674, "relative_end": **********.631674, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.631685, "relative_start": 0.8230750560760498, "end": **********.631685, "relative_end": **********.631685, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.63341, "relative_start": 0.8248000144958496, "end": **********.63341, "relative_end": **********.63341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.633427, "relative_start": 0.8248169422149658, "end": **********.633427, "relative_end": **********.633427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.633794, "relative_start": 0.8251841068267822, "end": **********.633794, "relative_end": **********.633794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.633806, "relative_start": 0.8251960277557373, "end": **********.633806, "relative_end": **********.633806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.634107, "relative_start": 0.8254971504211426, "end": **********.634107, "relative_end": **********.634107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.634118, "relative_start": 0.8255081176757812, "end": **********.634118, "relative_end": **********.634118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.634999, "relative_start": 0.8263890743255615, "end": **********.634999, "relative_end": **********.634999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.635016, "relative_start": 0.8264060020446777, "end": **********.635016, "relative_end": **********.635016, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.635587, "relative_start": 0.826977014541626, "end": **********.635587, "relative_end": **********.635587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.635607, "relative_start": 0.8269970417022705, "end": **********.635607, "relative_end": **********.635607, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.636161, "relative_start": 0.8275511264801025, "end": **********.636161, "relative_end": **********.636161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.636179, "relative_start": 0.8275690078735352, "end": **********.636179, "relative_end": **********.636179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.636816, "relative_start": 0.8282060623168945, "end": **********.636816, "relative_end": **********.636816, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.636834, "relative_start": 0.8282239437103271, "end": **********.636834, "relative_end": **********.636834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.637402, "relative_start": 0.8287920951843262, "end": **********.637402, "relative_end": **********.637402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.637416, "relative_start": 0.8288059234619141, "end": **********.637416, "relative_end": **********.637416, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.63791, "relative_start": 0.8292999267578125, "end": **********.63791, "relative_end": **********.63791, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.637932, "relative_start": 0.829322099685669, "end": **********.637932, "relative_end": **********.637932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.638614, "relative_start": 0.8300039768218994, "end": **********.638614, "relative_end": **********.638614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.638723, "relative_start": 0.8301129341125488, "end": **********.638723, "relative_end": **********.638723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.639362, "relative_start": 0.8307521343231201, "end": **********.639362, "relative_end": **********.639362, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.639381, "relative_start": 0.8307709693908691, "end": **********.639381, "relative_end": **********.639381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.639837, "relative_start": 0.8312270641326904, "end": **********.639837, "relative_end": **********.639837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.639853, "relative_start": 0.8312430381774902, "end": **********.639853, "relative_end": **********.639853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: layouts.default", "start": **********.640881, "relative_start": 0.8322710990905762, "end": **********.640881, "relative_end": **********.640881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: layouts.default", "start": **********.640912, "relative_start": 0.8323020935058594, "end": **********.640912, "relative_end": **********.640912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.646657, "relative_start": 0.8380470275878906, "end": **********.646657, "relative_end": **********.646657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.647094, "relative_start": 0.8384840488433838, "end": **********.647094, "relative_end": **********.647094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.64711, "relative_start": 0.8385000228881836, "end": **********.64711, "relative_end": **********.64711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.648066, "relative_start": 0.8394560813903809, "end": **********.648066, "relative_end": **********.648066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.648301, "relative_start": 0.8396909236907959, "end": **********.648301, "relative_end": **********.648301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.648321, "relative_start": 0.8397109508514404, "end": **********.648321, "relative_end": **********.648321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.649194, "relative_start": 0.8405840396881104, "end": **********.649194, "relative_end": **********.649194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.649416, "relative_start": 0.8408060073852539, "end": **********.649416, "relative_end": **********.649416, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.649434, "relative_start": 0.8408241271972656, "end": **********.649434, "relative_end": **********.649434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.650111, "relative_start": 0.841500997543335, "end": **********.650111, "relative_end": **********.650111, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.650231, "relative_start": 0.841620922088623, "end": **********.650231, "relative_end": **********.650231, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.650244, "relative_start": 0.8416340351104736, "end": **********.650244, "relative_end": **********.650244, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.650779, "relative_start": 0.8421690464019775, "end": **********.650779, "relative_end": **********.650779, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.65091, "relative_start": 0.8422999382019043, "end": **********.65091, "relative_end": **********.65091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.650921, "relative_start": 0.8423111438751221, "end": **********.650921, "relative_end": **********.650921, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.65145, "relative_start": 0.8428399562835693, "end": **********.65145, "relative_end": **********.65145, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.651552, "relative_start": 0.8429419994354248, "end": **********.651552, "relative_end": **********.651552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.651563, "relative_start": 0.8429529666900635, "end": **********.651563, "relative_end": **********.651563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.652075, "relative_start": 0.8434650897979736, "end": **********.652075, "relative_end": **********.652075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.652248, "relative_start": 0.8436379432678223, "end": **********.652248, "relative_end": **********.652248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.652349, "relative_start": 0.8437390327453613, "end": **********.652349, "relative_end": **********.652349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.65236, "relative_start": 0.84375, "end": **********.65236, "relative_end": **********.65236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.652908, "relative_start": 0.8442981243133545, "end": **********.652908, "relative_end": **********.652908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.653015, "relative_start": 0.844404935836792, "end": **********.653015, "relative_end": **********.653015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.653026, "relative_start": 0.8444161415100098, "end": **********.653026, "relative_end": **********.653026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.653533, "relative_start": 0.8449230194091797, "end": **********.653533, "relative_end": **********.653533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.653638, "relative_start": 0.8450279235839844, "end": **********.653638, "relative_end": **********.653638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.653649, "relative_start": 0.8450391292572021, "end": **********.653649, "relative_end": **********.653649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.654142, "relative_start": 0.8455319404602051, "end": **********.654142, "relative_end": **********.654142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.654247, "relative_start": 0.8456370830535889, "end": **********.654247, "relative_end": **********.654247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.654258, "relative_start": 0.8456480503082275, "end": **********.654258, "relative_end": **********.654258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.655131, "relative_start": 0.8465211391448975, "end": **********.655131, "relative_end": **********.655131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.655243, "relative_start": 0.8466329574584961, "end": **********.655243, "relative_end": **********.655243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.655253, "relative_start": 0.8466429710388184, "end": **********.655253, "relative_end": **********.655253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.656499, "relative_start": 0.8478889465332031, "end": **********.656499, "relative_end": **********.656499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.656668, "relative_start": 0.8480579853057861, "end": **********.656668, "relative_end": **********.656668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.656688, "relative_start": 0.8480780124664307, "end": **********.656688, "relative_end": **********.656688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.658173, "relative_start": 0.8495631217956543, "end": **********.658173, "relative_end": **********.658173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "eloquent.booting: App\\Models\\ConsumableAssignment", "start": **********.658777, "relative_start": 0.8501670360565186, "end": **********.658777, "relative_end": **********.658777, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booting"}, {"label": "eloquent.booted: App\\Models\\ConsumableAssignment", "start": **********.658896, "relative_start": 0.8502860069274902, "end": **********.658896, "relative_end": **********.658896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.66137, "relative_start": 0.8527600765228271, "end": **********.66137, "relative_end": **********.66137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.663332, "relative_start": 0.8547220230102539, "end": **********.663332, "relative_end": **********.663332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "eloquent.booting: App\\Models\\AccessoryCheckout", "start": **********.663933, "relative_start": 0.855323076248169, "end": **********.663933, "relative_end": **********.663933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booting"}, {"label": "eloquent.booted: App\\Models\\AccessoryCheckout", "start": **********.664013, "relative_start": 0.855402946472168, "end": **********.664013, "relative_end": **********.664013, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.664607, "relative_start": 0.8559970855712891, "end": **********.664607, "relative_end": **********.664607, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.667089, "relative_start": 0.8584790229797363, "end": **********.667089, "relative_end": **********.667089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.667609, "relative_start": 0.8589990139007568, "end": **********.667609, "relative_end": **********.667609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.66933, "relative_start": 0.8607199192047119, "end": **********.66933, "relative_end": **********.66933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "eloquent.booting: App\\Models\\AssetModel", "start": **********.67021, "relative_start": 0.8615999221801758, "end": **********.67021, "relative_end": **********.67021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booting"}, {"label": "eloquent.booted: App\\Models\\AssetModel", "start": **********.670343, "relative_start": 0.8617329597473145, "end": **********.670343, "relative_end": **********.670343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.67069, "relative_start": 0.8620800971984863, "end": **********.67069, "relative_end": **********.67069, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.67372, "relative_start": 0.865109920501709, "end": **********.67372, "relative_end": **********.67372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.674989, "relative_start": 0.8663790225982666, "end": **********.674989, "relative_end": **********.674989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.677137, "relative_start": 0.8685269355773926, "end": **********.677137, "relative_end": **********.677137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Log\\Events\\MessageLogged", "start": **********.677739, "relative_start": 0.869128942489624, "end": **********.677739, "relative_end": **********.677739, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Log\\Events\\MessageLogged"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.677929, "relative_start": 0.869318962097168, "end": **********.677929, "relative_end": **********.677929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.677947, "relative_start": 0.8693370819091797, "end": **********.677947, "relative_end": **********.677947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.678804, "relative_start": 0.8701939582824707, "end": **********.678804, "relative_end": **********.678804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.692561, "relative_start": 0.88395094871521, "end": **********.692561, "relative_end": **********.692561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.6926, "relative_start": 0.8839900493621826, "end": **********.6926, "relative_end": **********.6926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.694021, "relative_start": 0.8854110240936279, "end": **********.694021, "relative_end": **********.694021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.694253, "relative_start": 0.8856430053710938, "end": **********.694253, "relative_end": **********.694253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.694278, "relative_start": 0.8856680393218994, "end": **********.694278, "relative_end": **********.694278, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.695121, "relative_start": 0.8865110874176025, "end": **********.695121, "relative_end": **********.695121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.695145, "relative_start": 0.8865349292755127, "end": **********.695145, "relative_end": **********.695145, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.696228, "relative_start": 0.8876180648803711, "end": **********.696228, "relative_end": **********.696228, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.696363, "relative_start": 0.8877530097961426, "end": **********.696363, "relative_end": **********.696363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.696384, "relative_start": 0.8877739906311035, "end": **********.696384, "relative_end": **********.696384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.697167, "relative_start": 0.888556957244873, "end": **********.697167, "relative_end": **********.697167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.697188, "relative_start": 0.888577938079834, "end": **********.697188, "relative_end": **********.697188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.698175, "relative_start": 0.8895649909973145, "end": **********.698175, "relative_end": **********.698175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.698298, "relative_start": 0.8896880149841309, "end": **********.698298, "relative_end": **********.698298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.698318, "relative_start": 0.8897080421447754, "end": **********.698318, "relative_end": **********.698318, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.698982, "relative_start": 0.8903720378875732, "end": **********.698982, "relative_end": **********.698982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.699002, "relative_start": 0.8903920650482178, "end": **********.699002, "relative_end": **********.699002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.69993, "relative_start": 0.891319990158081, "end": **********.69993, "relative_end": **********.69993, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.700036, "relative_start": 0.8914260864257812, "end": **********.700036, "relative_end": **********.700036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.700053, "relative_start": 0.8914430141448975, "end": **********.700053, "relative_end": **********.700053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.700671, "relative_start": 0.8920609951019287, "end": **********.700671, "relative_end": **********.700671, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.700689, "relative_start": 0.8920791149139404, "end": **********.700689, "relative_end": **********.700689, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.701444, "relative_start": 0.892833948135376, "end": **********.701444, "relative_end": **********.701444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.701569, "relative_start": 0.8929591178894043, "end": **********.701569, "relative_end": **********.701569, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.701585, "relative_start": 0.8929750919342041, "end": **********.701585, "relative_end": **********.701585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.702371, "relative_start": 0.8937609195709229, "end": **********.702371, "relative_end": **********.702371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.702504, "relative_start": 0.8938939571380615, "end": **********.702504, "relative_end": **********.702504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.702518, "relative_start": 0.8939080238342285, "end": **********.702518, "relative_end": **********.702518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.70304, "relative_start": 0.8944299221038818, "end": **********.70304, "relative_end": **********.70304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.703056, "relative_start": 0.8944461345672607, "end": **********.703056, "relative_end": **********.703056, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.703664, "relative_start": 0.8950541019439697, "end": **********.703664, "relative_end": **********.703664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.70368, "relative_start": 0.8950700759887695, "end": **********.70368, "relative_end": **********.70368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.706079, "relative_start": 0.8974690437316895, "end": **********.706079, "relative_end": **********.706079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.708317, "relative_start": 0.8997070789337158, "end": **********.708317, "relative_end": **********.708317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.708533, "relative_start": 0.8999230861663818, "end": **********.708533, "relative_end": **********.708533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.708547, "relative_start": 0.8999371528625488, "end": **********.708547, "relative_end": **********.708547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.709053, "relative_start": 0.9004430770874023, "end": **********.709053, "relative_end": **********.709053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.709067, "relative_start": 0.9004571437835693, "end": **********.709067, "relative_end": **********.709067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.709444, "relative_start": 0.9008340835571289, "end": **********.709444, "relative_end": **********.709444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.709457, "relative_start": 0.9008469581604004, "end": **********.709457, "relative_end": **********.709457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.709816, "relative_start": 0.9012060165405273, "end": **********.709816, "relative_end": **********.709816, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.709828, "relative_start": 0.9012179374694824, "end": **********.709828, "relative_end": **********.709828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.71019, "relative_start": 0.9015800952911377, "end": **********.71019, "relative_end": **********.71019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.710202, "relative_start": 0.9015920162200928, "end": **********.710202, "relative_end": **********.710202, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.710545, "relative_start": 0.9019351005554199, "end": **********.710545, "relative_end": **********.710545, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.710557, "relative_start": 0.901947021484375, "end": **********.710557, "relative_end": **********.710557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.710894, "relative_start": 0.9022841453552246, "end": **********.710894, "relative_end": **********.710894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.710905, "relative_start": 0.9022951126098633, "end": **********.710905, "relative_end": **********.710905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.711497, "relative_start": 0.9028871059417725, "end": **********.711497, "relative_end": **********.711497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.711635, "relative_start": 0.9030251502990723, "end": **********.711635, "relative_end": **********.711635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.711646, "relative_start": 0.9030361175537109, "end": **********.711646, "relative_end": **********.711646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.712233, "relative_start": 0.903623104095459, "end": **********.712233, "relative_end": **********.712233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.712357, "relative_start": 0.9037470817565918, "end": **********.712357, "relative_end": **********.712357, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.712368, "relative_start": 0.9037580490112305, "end": **********.712368, "relative_end": **********.712368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.712903, "relative_start": 0.9042930603027344, "end": **********.712903, "relative_end": **********.712903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.713157, "relative_start": 0.9045469760894775, "end": **********.713157, "relative_end": **********.713157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.71342, "relative_start": 0.9048099517822266, "end": **********.71342, "relative_end": **********.71342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.713658, "relative_start": 0.905048131942749, "end": **********.713658, "relative_end": **********.713658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.713855, "relative_start": 0.9052450656890869, "end": **********.713855, "relative_end": **********.713855, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.714066, "relative_start": 0.9054560661315918, "end": **********.714066, "relative_end": **********.714066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.714131, "relative_start": 0.9055211544036865, "end": **********.714131, "relative_end": **********.714131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.714141, "relative_start": 0.9055309295654297, "end": **********.714141, "relative_end": **********.714141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.714687, "relative_start": 0.9060771465301514, "end": **********.714687, "relative_end": **********.714687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.714761, "relative_start": 0.9061510562896729, "end": **********.714761, "relative_end": **********.714761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.714771, "relative_start": 0.9061610698699951, "end": **********.714771, "relative_end": **********.714771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.715267, "relative_start": 0.9066569805145264, "end": **********.715267, "relative_end": **********.715267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.715326, "relative_start": 0.9067161083221436, "end": **********.715326, "relative_end": **********.715326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.715336, "relative_start": 0.9067261219024658, "end": **********.715336, "relative_end": **********.715336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.715824, "relative_start": 0.9072139263153076, "end": **********.715824, "relative_end": **********.715824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.715895, "relative_start": 0.9072849750518799, "end": **********.715895, "relative_end": **********.715895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.715905, "relative_start": 0.9072949886322021, "end": **********.715905, "relative_end": **********.715905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.716402, "relative_start": 0.9077920913696289, "end": **********.716402, "relative_end": **********.716402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.716501, "relative_start": 0.907891035079956, "end": **********.716501, "relative_end": **********.716501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.716511, "relative_start": 0.9079010486602783, "end": **********.716511, "relative_end": **********.716511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.717003, "relative_start": 0.908393144607544, "end": **********.717003, "relative_end": **********.717003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.717095, "relative_start": 0.908484935760498, "end": **********.717095, "relative_end": **********.717095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.717104, "relative_start": 0.9084939956665039, "end": **********.717104, "relative_end": **********.717104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.717611, "relative_start": 0.9090011119842529, "end": **********.717611, "relative_end": **********.717611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.717713, "relative_start": 0.9091031551361084, "end": **********.717713, "relative_end": **********.717713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.717724, "relative_start": 0.9091141223907471, "end": **********.717724, "relative_end": **********.717724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.718227, "relative_start": 0.9096169471740723, "end": **********.718227, "relative_end": **********.718227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.718276, "relative_start": 0.9096660614013672, "end": **********.718276, "relative_end": **********.718276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.718286, "relative_start": 0.9096760749816895, "end": **********.718286, "relative_end": **********.718286, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.718637, "relative_start": 0.910027027130127, "end": **********.718637, "relative_end": **********.718637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.718648, "relative_start": 0.9100379943847656, "end": **********.718648, "relative_end": **********.718648, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.719109, "relative_start": 0.910499095916748, "end": **********.719109, "relative_end": **********.719109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.719837, "relative_start": 0.9112269878387451, "end": **********.719837, "relative_end": **********.719837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.720093, "relative_start": 0.9114830493927002, "end": **********.720093, "relative_end": **********.720093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.72031, "relative_start": 0.9117000102996826, "end": **********.72031, "relative_end": **********.72031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.720514, "relative_start": 0.9119040966033936, "end": **********.720514, "relative_end": **********.720514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.720711, "relative_start": 0.9121010303497314, "end": **********.720711, "relative_end": **********.720711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.720911, "relative_start": 0.9123010635375977, "end": **********.720911, "relative_end": **********.720911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.721119, "relative_start": 0.9125089645385742, "end": **********.721119, "relative_end": **********.721119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.721763, "relative_start": 0.9131529331207275, "end": **********.721763, "relative_end": **********.721763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.722369, "relative_start": 0.9137589931488037, "end": **********.722369, "relative_end": **********.722369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.722623, "relative_start": 0.914013147354126, "end": **********.722623, "relative_end": **********.722623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.722697, "relative_start": 0.9140870571136475, "end": **********.722697, "relative_end": **********.722697, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.722709, "relative_start": 0.9140989780426025, "end": **********.722709, "relative_end": **********.722709, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.723203, "relative_start": 0.914592981338501, "end": **********.723203, "relative_end": **********.723203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.723216, "relative_start": 0.9146060943603516, "end": **********.723216, "relative_end": **********.723216, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.724067, "relative_start": 0.915457010269165, "end": **********.724067, "relative_end": **********.724067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.724161, "relative_start": 0.915550947189331, "end": **********.724161, "relative_end": **********.724161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.724172, "relative_start": 0.9155621528625488, "end": **********.724172, "relative_end": **********.724172, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.724668, "relative_start": 0.9160580635070801, "end": **********.724668, "relative_end": **********.724668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.724711, "relative_start": 0.9161009788513184, "end": **********.724711, "relative_end": **********.724711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: notifications", "start": **********.726353, "relative_start": 0.9177429676055908, "end": **********.726353, "relative_end": **********.726353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: notifications", "start": **********.726398, "relative_start": 0.***************, "end": **********.726398, "relative_end": **********.726398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Routing\\Events\\ResponsePrepared", "start": **********.732205, "relative_start": 0.****************, "end": **********.732205, "relative_end": **********.732205, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\ResponsePrepared"}], "nb_measures": 320}, "views": {"count": 95, "nb_templates": 95, "templates": [{"name": "1x dashboard", "param_count": null, "params": [], "start": **********.579855, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.phpdashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "dashboard"}, {"name": "91x 2548ffbf4d41f5cc86ea949d4376e84f::icon", "param_count": null, "params": [], "start": **********.591126, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Providers/../../resources/views/blade/icon.blade.php2548ffbf4d41f5cc86ea949d4376e84f::icon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fblade%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}, "render_count": 91, "name_original": "2548ffbf4d41f5cc86ea949d4376e84f::icon"}, {"name": "1x partials.bootstrap-table", "param_count": null, "params": [], "start": **********.624445, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/partials/bootstrap-table.blade.phppartials.bootstrap-table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fpartials%2Fbootstrap-table.blade.php&line=1", "ajax": false, "filename": "bootstrap-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.bootstrap-table"}, {"name": "1x layouts.default", "param_count": null, "params": [], "start": **********.64112, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/default.blade.phplayouts.default", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Flayouts%2Fdefault.blade.php&line=1", "ajax": false, "filename": "default.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.default"}, {"name": "1x notifications", "param_count": null, "params": [], "start": **********.726615, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/notifications.blade.phpnotifications", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fnotifications.blade.php&line=1", "ajax": false, "filename": "notifications.blade.php", "line": "?"}, "render_count": 1, "name_original": "notifications"}]}, "route": {"uri": "GET /", "middleware": "web, auth, breadcrumbs", "controller": "\\App\\Http\\Controllers\\DashboardController@index<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=27\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "home", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=27\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/DashboardController.php:27-53</a>"}, "queries": {"count": 30, "nb_statements": 30, "nb_visible_statements": 30, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.025669999999999995, "accumulated_duration_str": "25.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `assets` where `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 37}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 21, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.506331, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:37", "source": {"index": 19, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=37", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "37"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `deployable` = 0 and `pending` = 0 and `archived` = 1 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0, 0, 1], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.5136828, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:39", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=39", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "39"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`assigned_to` is null and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `deployable` = 1 and `pending` = 0 and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.517089, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:47", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=47", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "47"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assigned_to` > '0' and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["0"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.5203521, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:54", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=54", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "54"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `deployable` = 0 and `pending` = 0 and `archived` = 1 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0, 0, 1], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 61}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.523598, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:61", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=61", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "61"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `deployable` = 0 and `pending` = 1 and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0, 1, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 68}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.5269308, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:68", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=68", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "68"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `deployable` = 0 and `pending` = 0 and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0, 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 75}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.5320148, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:75", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=75", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "75"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `byod` = '1' and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 82}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.5345, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:82", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 82}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=82", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "82"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`next_audit_date` is not null and `assets`.`next_audit_date` between '2025-08-16' and '2025-08-16' and `assets`.`archived` = 0 and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16", "2025-08-16", 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 89}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.537153, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:89", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=89", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "89"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`next_audit_date` is not null and `assets`.`next_audit_date` < '2025-08-16' and `assets`.`archived` = 0 and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16", 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 96}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.540993, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:96", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=96", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "96"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`expected_checkin` is not null and `assets`.`expected_checkin` between '2025-08-16' and '2025-08-16' and `assets`.`archived` = 0 and `assets`.`assigned_to` is not null and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16", "2025-08-16", 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 103}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.544231, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:103", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=103", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "103"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`expected_checkin` is not null and `assets`.`expected_checkin` < '2025-08-16' and `assets`.`archived` = 0 and `assets`.`assigned_to` is not null and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16", 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 110}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.547893, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:110", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=110", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "110"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 33}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.555512, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:33", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=33", "ajax": false, "filename": "DashboardController.php", "line": "33"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `accessories` where `accessories`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 34}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.558648, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:34", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=34", "ajax": false, "filename": "DashboardController.php", "line": "34"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `license_seats` where `deleted_at` is null and `license_seats`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/License.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\License.php", "line": 452}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.5621948, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "License.php:452", "source": {"index": 16, "namespace": null, "name": "app/Models/License.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\License.php", "line": 452}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FModels%2FLicense.php&line=452", "ajax": false, "filename": "License.php", "line": "452"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `consumables` where `consumables`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.5646138, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:36", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=36", "ajax": false, "filename": "DashboardController.php", "line": "36"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `components` where `components`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 37}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.566537, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:37", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=37", "ajax": false, "filename": "DashboardController.php", "line": "37"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `users` where `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.570221, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:38", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=38", "ajax": false, "filename": "DashboardController.php", "line": "38"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 39}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.5843961, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "dashboard:39", "source": {"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fdashboard.blade.php&line=39", "ajax": false, "filename": "dashboard.blade.php", "line": "39"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`expected_checkin` is not null and `assets`.`expected_checkin` < '2025-08-16' and `assets`.`archived` = 0 and `assets`.`assigned_to` is not null and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16", 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 400}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.5966759, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "dashboard:400", "source": {"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 400}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fdashboard.blade.php&line=400", "ajax": false, "filename": "dashboard.blade.php", "line": "400"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `next_audit_date` < '2025-08-16 08:40:53' and `next_audit_date` is not null and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16 08:40:53"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 457}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.6004312, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "dashboard:457", "source": {"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 457}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fdashboard.blade.php&line=457", "ajax": false, "filename": "dashboard.blade.php", "line": "457"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `asset_eol_date` is not null and `asset_eol_date` <= '2025-08-16 08:40:53' and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16 08:40:53"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 513}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.604703, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "dashboard:513", "source": {"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 513}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fdashboard.blade.php&line=513", "ajax": false, "filename": "dashboard.blade.php", "line": "513"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `warranty_months` is not null and DATE_ADD(purchase_date, INTERVAL warranty_months MONTH) < NOW() and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 569}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.609914, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "dashboard:569", "source": {"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 569}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fdashboard.blade.php&line=569", "ajax": false, "filename": "dashboard.blade.php", "line": "569"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `consumables` where `qty` <= `min_amt` and `min_amt` > 0 and `consumables`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 625}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.613267, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "dashboard:625", "source": {"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fdashboard.blade.php&line=625", "ajax": false, "filename": "dashboard.blade.php", "line": "625"}, "connection": "snipeit", "explain": null}, {"sql": "select `consumables`.*, (select count(*) from `consumables_users` where `consumables`.`id` = `consumables_users`.`consumable_id`) as `consumable_assignments_count` from `consumables` where `min_amt` is not null and `consumables`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 748}, {"index": 16, "namespace": "view", "name": "layouts.default", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/default.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.660952, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Helper.php:748", "source": {"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 748}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHelpers%2FHelper.php&line=748", "ajax": false, "filename": "Helper.php", "line": "748"}, "connection": "snipeit", "explain": null}, {"sql": "select `accessories`.*, (select count(*) from `accessories_checkout` where `accessories`.`id` = `accessories_checkout`.`accessory_id`) as `checkouts_count` from `accessories` where `min_amt` is not null and `accessories`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 749}, {"index": 16, "namespace": "view", "name": "layouts.default", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/default.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.664323, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Helper.php:749", "source": {"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 749}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHelpers%2FHelper.php&line=749", "ajax": false, "filename": "Helper.php", "line": "749"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `components` where `min_amt` is not null and `components`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 750}, {"index": 16, "namespace": "view", "name": "layouts.default", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/default.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.6673908, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Helper.php:750", "source": {"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 750}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHelpers%2FHelper.php&line=750", "ajax": false, "filename": "Helper.php", "line": "750"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `models` where `min_amt` > 0 and `models`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 751}, {"index": 16, "namespace": "view", "name": "layouts.default", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/default.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.670519, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Helper.php:751", "source": {"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 751}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHelpers%2FHelper.php&line=751", "ajax": false, "filename": "Helper.php", "line": "751"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `licenses` where `min_amt` > 0 and `licenses`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 752}, {"index": 16, "namespace": "view", "name": "layouts.default", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/default.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.6744711, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Helper.php:752", "source": {"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 752}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHelpers%2FHelper.php&line=752", "ajax": false, "filename": "Helper.php", "line": "752"}, "connection": "snipeit", "explain": null}, {"sql": "select `status_labels`.*, (select count(*) from `assets` where `status_labels`.`id` = `assets`.`status_id` and `assets`.`deleted_at` is null) as `asset_count` from `status_labels` where `show_in_nav` = 1 and `status_labels`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "layouts.default", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/default.blade.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.705239, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "layouts.default:1004", "source": {"index": 15, "namespace": "view", "name": "layouts.default", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/default.blade.php", "line": 1004}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Flayouts%2Fdefault.blade.php&line=1004", "ajax": false, "filename": "default.blade.php", "line": "1004"}, "connection": "snipeit", "explain": null}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "logs": {"count": 0, "messages": []}, "files": {"messages": [{"message": "'\\server.php',", "is_string": true}, {"message": "'\\public\\index.php',", "is_string": true}, {"message": "'\\vendor\\autoload.php',", "is_string": true}, {"message": "'\\vendor\\composer\\autoload_real.php',", "is_string": true}, {"message": "'\\vendor\\composer\\platform_check.php',", "is_string": true}, {"message": "'\\vendor\\composer\\ClassLoader.php',", "is_string": true}, {"message": "'\\vendor\\composer\\autoload_static.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\deprecation-contracts\\function.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-mbstring\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-mbstring\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-normalizer\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-normalizer\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-ctype\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-ctype\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-grapheme\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-grapheme\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\string\\Resources\\functions.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-php83\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-php83\\bootstrap81.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Resources\\functions\\dump.php',", "is_string": true}, {"message": "'\\vendor\\ralouphie\\getallheaders\\src\\getallheaders.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-php80\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-idn\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-idn\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\react\\promise\\src\\functions_include.php',", "is_string": true}, {"message": "'\\vendor\\react\\promise\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\functions_include.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\prompts\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\termwind\\src\\Functions.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\clock\\Resources\\now.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-uuid\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-uuid\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Resources\\functions.php',", "is_string": true}, {"message": "'\\vendor\\ramsey\\uuid\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\fakerphp\\faker\\src\\Faker\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\mtdowling\\jmespath.php\\src\\JmesPath.php',", "is_string": true}, {"message": "'\\vendor\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock\\autoload.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\php-text-template\\src\\Template.php',", "is_string": true}, {"message": "'\\vendor\\phpseclib\\phpseclib\\phpseclib\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\flare-client-php\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-php81\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\aws\\aws-sdk-php\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\ezyang\\htmlpurifier\\library\\HTMLPurifier.composer.php',", "is_string": true}, {"message": "'\\vendor\\phpstan\\phpstan\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php',", "is_string": true}, {"message": "'\\vendor\\psy\\psysh\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\helpers\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\league\\csv\\src\\functions_include.php',", "is_string": true}, {"message": "'\\vendor\\league\\csv\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\mockery\\mockery\\library\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\mockery\\mockery\\library\\Mockery.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\collision\\src\\Adapters\\Phpunit\\Autoload.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Runner\\Version.php',", "is_string": true}, {"message": "'\\vendor\\sebastian\\version\\src\\Version.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\collision\\src\\Adapters\\Phpunit\\Subscribers\\EnsurePrinterIsRegisteredSubscriber.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Event\\Events\\Application\\StartedSubscriber.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Event\\Subscriber.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\autoload.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\autoload-php7.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\src\\Compat.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\namespaced.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\sodium_compat.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\constants.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\php84compat_const.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\php84compat.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\stream-xchacha20.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\ristretto255.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock-phpunit\\autoload.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Rule\\InvocationOrder.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\SelfDescribing.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Invocation.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Interface\\MockObject.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Interface\\Stub.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\InvocationMocker.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\InvocationStubber.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\MethodNameMatch.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\ParametersMatch.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\Stub.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\Identity.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Rule\\MethodName.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\InvocationHandler.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock-phpunit\\classes\\MockDisablerPHPUnit10.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Event\\Events\\Test\\Lifecycle\\FinishedSubscriber.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock-phpunit\\classes\\DefaultArgumentRemoverReturnTypes100.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock-phpunit\\classes\\MockObjectProxyReturnTypes100.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-backup\\src\\Helpers\\functions.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\helpers.php',", "is_string": true}, {"message": "'\\bootstrap\\app.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Container\\Container.php',", "is_string": true}, {"message": "'\\vendor\\psr\\container\\src\\ContainerInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Foundation\\Application.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Foundation\\CachesConfiguration.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Foundation\\CachesRoutes.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-kernel\\HttpKernelInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\EventServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\Context\\ContextServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RoutingServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Http\\Kernel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\InteractsWithTime.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Http\\Kernel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Container\\ContextualAttribute.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Tappable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Routing\\BindingRegistrar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Routing\\Registrar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ReflectsClosures.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Events\\Dispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollectionInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Request.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\Request.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\AcceptHeader.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\AcceptHeaderItem.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\FileBag.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\ParameterBag.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\HeaderBag.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\HeaderUtils.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\InputBag.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\ServerBag.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Concerns\\CanBePrecognitive.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Concerns\\InteractsWithContentTypes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Concerns\\InteractsWithFlashData.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Concerns\\InteractsWithInput.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Dumpable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\InteractsWithData.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Conditionable\\Traits\\Conditionable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Arrayable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Carbon.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Carbon.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Date.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Boundaries.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Comparison.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Converter.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\ToStringFormat.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Creator.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\ObjectInitialisation.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\LocalFactory.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Difference.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Macro.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Mixin.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\MagicParameter.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Modifiers.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Mutability.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Cast.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Options.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\StaticOptions.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Localization.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\StaticLocalization.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Rounding.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\IntervalRounding.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Serialization.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Test.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Timestamp.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Units.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Week.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonTimeZone.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonInterval.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\IntervalStep.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonConverterInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\FactoryImmutable.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\clock\\ClockInterface.php',", "is_string": true}, {"message": "'\\vendor\\psr\\clock\\src\\ClockInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonImmutable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadEnvironmentVariables.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Env.php',", "is_string": true}, {"message": "'\\vendor\\phpoption\\phpoption\\src\\PhpOption\\Option.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\RepositoryBuilder.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\ServerConstAdapter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\AdapterInterface.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\ReaderInterface.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\WriterInterface.php',", "is_string": true}, {"message": "'\\vendor\\phpoption\\phpoption\\src\\PhpOption\\Some.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\EnvConstAdapter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\PutenvAdapter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\MultiReader.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\MultiWriter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\ImmutableWriter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\AdapterRepository.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\RepositoryInterface.php',", "is_string": true}, {"message": "'\\vendor\\phpoption\\phpoption\\src\\PhpOption\\None.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php',", "is_string": true}, {"message": "'\\bootstrap\\cache\\config.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Config\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Config\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\EnvironmentDetector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\AliasLoader.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\CanBeEscapedWhenCastToString.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Enumerable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Jsonable.php',", "is_string": true}, {"message": "'\\bootstrap\\cache\\packages.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php',", "is_string": true}, {"message": "'\\bootstrap\\cache\\services.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\CookieServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasEvents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasGlobalScopes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasRelationships.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasTimestamps.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasUniqueIds.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HidesAttributes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\PreventsCircularRecursion.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\HasCollection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Broadcasting\\HasBroadcastChannel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Queue\\QueueableEntity.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Routing\\UrlRoutable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\SerializableClosure.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Signers\\Hmac.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Contracts\\Signer.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Serializers\\Signed.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Contracts\\Serializable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FoundationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\AggregateServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Testing\\ParallelTestingServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Testing\\Concerns\\TestDatabases.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\DeferrableProvider.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\AbstractCloner.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\ClonerInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\HtmlDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Dumper\\HtmlDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Dumper\\CliDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Dumper\\AbstractDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Dumper\\DataDumperInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\DumperInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Concerns\\ResolvesDumpSource.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\VarCloner.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\HigherOrderTapProxy.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Caster\\ReflectionCaster.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\VarDumper.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Uri.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Htmlable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Responsable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Reflector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\PaginationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\PaginationState.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\Paginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Pagination\\Paginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\CursorPaginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractCursorPaginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Pagination\\CursorPaginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\arietimmerman\\laravel-scim-server\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-dompdf\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\eduardokum\\laravel-mail-auto-embed\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\intervention\\image\\src\\Intervention\\Image\\ImageServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\intervention\\image\\src\\Intervention\\Image\\ImageServiceProviderLaravelRecent.php',", "is_string": true}, {"message": "'\\vendor\\laravel-notification-channels\\google-chat\\src\\GoogleChatServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel-notification-channels\\microsoft-teams\\src\\MicrosoftTeamsServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Notification.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Manager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Notifications\\Dispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Notifications\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\Passport.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\ContextualBindingBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Container\\ContextualBindingBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Auth.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\slack-notification-channel\\src\\SlackChannelServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\ui\\src\\UiServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\LivewireServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\LivewireManager.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\EventBus.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\PersistentMiddleware\\PersistentMiddleware.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\Mechanism.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\FrontendAssets\\FrontendAssets.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendBlade.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\CompileLivewireTags\\CompileLivewireTags.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\RenderComponent.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\DataStore.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Laravel\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\collision\\src\\Adapters\\Laravel\\CollisionServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\phpinsights\\src\\Application\\Adapters\\Laravel\\InsightsServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\phpinsights\\src\\Application\\Injectors\\Repositories.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\termwind\\src\\Laravel\\TermwindServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\osa-eg\\laravel-teams-notification\\src\\LaravelTeamsNotificationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\pragmarx\\google2fa-laravel\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-backup\\src\\BackupServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessAssets.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessBladeComponents.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessCommands.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessConfigs.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessInertia.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessMigrations.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessRoutes.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessServiceProviders.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessTranslations.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessViewComposers.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessViews.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessViewSharedData.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Package.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasAssets.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasBladeComponents.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasCommands.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasConfigs.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasInertia.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasInstallCommand.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasMigrations.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasRoutes.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasServiceProviders.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasTranslations.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasViewComposers.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasViews.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasViewSharedData.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\IgnitionServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\ignition\\src\\Config\\IgnitionConfig.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\ignition\\src\\Config\\FileConfigManager.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\ignition\\src\\Contracts\\ConfigManager.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\error-solutions\\src\\SolutionProviderRepository.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\error-solutions\\src\\Contracts\\SolutionProviderRepository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Log.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\ParsesLogConfiguration.php',", "is_string": true}, {"message": "'\\vendor\\psr\\log\\src\\LoggerInterface.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Level.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-signal-aware-command\\src\\SignalAwareCommandServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\BreadcrumbsServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Route.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CreatesRegularExpressionRouteConstraints.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\FiltersControllerMiddleware.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php',", "is_string": true}, {"message": "'\\vendor\\unicodeveloper\\laravel-password\\src\\DumbPasswordServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\AppServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\AuthServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\AuthServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\EventServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\RouteServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\SettingsServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\ValidationServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\BladeServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\LivewireServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\MacroServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\SamlServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\BreadcrumbsServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\ConnectionResolverInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Renderer\\Listener.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteBinding.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\debugbar-routes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteAction.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteUri.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteGroup.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\console\\Application.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\service-contracts\\ResetInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Console\\Application.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Event.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\routes\\web.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\ui\\src\\AuthRouteMethods.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\AboutCommand.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\console\\Command\\Command.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\console\\Command\\SignalableCommandInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\ConfiguresPrompts.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\HasParameters.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\InteractsWithIO.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\InteractsWithSignals.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\PromptsForMissingInput.php',", "is_string": true}, {"message": "'\\vendor\\composer\\InstalledVersions.php',", "is_string": true}, {"message": "'\\vendor\\composer\\installed.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\console\\Output\\OutputInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Stringable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Blade.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Compiler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesAuthorizations.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesClasses.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesComments.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesComponents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesConditionals.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesEchos.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesErrors.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesFragments.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesHelpers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesIncludes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesInjections.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesJson.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesJs.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesLayouts.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesLoops.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesRawPhp.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesSessions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesStacks.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesStyles.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesTranslations.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesUseStatements.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\CompilerInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\EngineResolver.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\CompileLivewireTags\\LivewireTagPrecompiler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\ComponentHookRegistry.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportWireModelingNestedComponents\\SupportWireModelingNestedComponents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\ComponentHook.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportMultipleRootElementDetection\\SupportMultipleRootElementDetection.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\SupportDisablingBackButtonCache.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportNestedComponentListeners\\SupportNestedComponentListeners.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportMorphAwareIfStatement\\SupportMorphAwareIfStatement.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Livewire.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportAutoInjectedAssets\\SupportAutoInjectedAssets.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportComputed\\SupportLegacyComputedPropertySyntax.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportNestingComponents\\SupportNestingComponents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportScriptsAndAssets\\SupportScriptsAndAssets.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportBladeAttributes\\SupportBladeAttributes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ComponentAttributeBag.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportConsoleCommands\\SupportConsoleCommands.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\View\\View.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Renderable.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportReactiveProps\\SupportReactiveProps.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportFileDownloads\\SupportFileDownloads.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportJsEvaluation\\SupportJsEvaluation.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportQueryString\\SupportQueryString.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportFileUploads\\SupportFileUploads.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportTeleporting\\SupportTeleporting.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportLazyLoading\\SupportLazyLoading.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportFormObjects\\SupportFormObjects.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportAttributes\\SupportAttributes.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination\\SupportPagination.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportValidation\\SupportValidation.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportIsolating\\SupportIsolating.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportRedirects\\SupportRedirects.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportStreaming\\SupportStreaming.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportNavigate\\SupportNavigate.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Vite.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportEntangle\\SupportEntangle.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportLocales\\SupportLocales.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportTesting\\SupportTesting.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\SupportModels.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportEvents\\SupportEvents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportLegacyModels\\SupportLegacyModels.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportWireables\\SupportWireables.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Translator.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation-contracts\\TranslatorInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Translator.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\MessageCatalogue.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\MessageCatalogueInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\MetadataAwareInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\CatalogueMetadataAwareInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\TranslatorBagInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation-contracts\\LocaleAwareInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\lazy\\Carbon\\TranslatorStrongType.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\AbstractTranslator.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\TranslatorStrongTypeInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Loader\\ArrayLoader.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Loader\\LoaderInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\MessageFormatter\\MessageFormatterMapper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Formatter\\MessageFormatterInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\lazy\\Carbon\\MessageFormatter\\MessageFormatterMapperStrongType.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Formatter\\MessageFormatter.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Formatter\\IntlFormatter.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Formatter\\IntlFormatterInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\IdentityTranslator.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation-contracts\\TranslatorTrait.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Lang\\en.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Lang\\en_US.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonPeriod.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\lazy\\Carbon\\UnprotectedDatePeriod.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Date.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\DateFactory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Events\\LocaleUpdated.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-backup\\src\\Notifications\\EventHandler.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-backup\\src\\Listeners\\EncryptBackupArchive.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\ignition-routes.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Http\\Controllers\\HealthCheckController.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Http\\Controllers\\ExecuteSolutionController.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Validation\\ValidatesRequests.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Http\\Controllers\\UpdateConfigController.php',", "is_string": true}, {"message": "'\\app\\Exceptions\\Handler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Debug\\ExceptionHandler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\ReportableHandler.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\DumpRecorder\\DumpRecorder.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\DumpRecorder\\MultiDumpHandler.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\JobRecorder\\JobRecorder.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\LogRecorder\\LogRecorder.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\QueryRecorder\\QueryRecorder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\QueueServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Serializers\\Native.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\QueueManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Queue\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Queue\\Monitor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Validator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Validation\\Factory.php',", "is_string": true}, {"message": "'\\app\\Providers\\SnipeTranslationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Translation\\TranslationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Translation\\FileLoader.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Translation\\Loader.php',", "is_string": true}, {"message": "'\\app\\Services\\SnipeTranslator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Translation\\Translator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\NamespacedItemResolver.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Translation\\Translator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifierInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\PresenceVerifierInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Routing\\UrlGenerator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Optional.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\NullHandler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\Handler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\HandlerInterface.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Logger.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\ResettableInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\Logger.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\LogRecord.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\JsonSerializableDateTimeImmutable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\Context\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\Events\\MessageLogged.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\LogRecorder\\LogMessage.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\URL.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Schema.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ConfigurationUrlParser.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DetectsConcurrencyErrors.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DetectsLostConnections.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\ConnectionInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Grammars\\MySqlGrammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Grammars\\Grammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Grammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\CompilesJsonPaths.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseTransactionsManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Events\\ConnectionEstablished.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Events\\ConnectionEvent.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Grammars\\Grammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php',", "is_string": true}, {"message": "'\\app\\Models\\Asset.php',", "is_string": true}, {"message": "'\\app\\Models\\Depreciable.php',", "is_string": true}, {"message": "'\\app\\Models\\SnipeModel.php',", "is_string": true}, {"message": "'\\app\\Models\\CompanyableTrait.php',", "is_string": true}, {"message": "'\\app\\Models\\Traits\\HasUploads.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Factories\\HasFactory.php',", "is_string": true}, {"message": "'\\app\\Models\\Loggable.php',", "is_string": true}, {"message": "'\\app\\Models\\Requestable.php',", "is_string": true}, {"message": "'\\app\\Presenters\\Presentable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\SoftDeletes.php',", "is_string": true}, {"message": "'\\vendor\\watson\\validating\\src\\ValidatingTrait.php',", "is_string": true}, {"message": "'\\vendor\\watson\\validating\\src\\Injectors\\UniqueInjector.php',", "is_string": true}, {"message": "'\\app\\Http\\Traits\\UniqueUndeletedTrait.php',", "is_string": true}, {"message": "'\\app\\Models\\Traits\\Acceptable.php',", "is_string": true}, {"message": "'\\app\\Models\\Traits\\Searchable.php',", "is_string": true}, {"message": "'\\app\\Models\\CompanyableScope.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Scope.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\SoftDeletingScope.php',", "is_string": true}, {"message": "'\\vendor\\watson\\validating\\src\\ValidatingObserver.php',", "is_string": true}, {"message": "'\\app\\Observers\\AssetObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\User.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Authenticatable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Auth\\Access\\Authorizable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Passwords\\CanResetPassword.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\HasApiTokens.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Notifiable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\HasDatabaseNotifications.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\RoutesNotifications.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Authenticatable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Access\\Authorizable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\CanResetPassword.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Translation\\HasLocalePreference.php',", "is_string": true}, {"message": "'\\app\\Observers\\UserObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\Accessory.php',", "is_string": true}, {"message": "'\\app\\Observers\\AccessoryObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\Component.php',", "is_string": true}, {"message": "'\\app\\Observers\\ComponentObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\Consumable.php',", "is_string": true}, {"message": "'\\app\\Observers\\ConsumableObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\License.php',", "is_string": true}, {"message": "'\\app\\Observers\\LicenseObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\Setting.php',", "is_string": true}, {"message": "'\\app\\Observers\\SettingObserver.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Gate.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\HandlesAuthorization.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Access\\Gate.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Unit.php',", "is_string": true}, {"message": "'\\app\\Listeners\\LogListener.php',", "is_string": true}, {"message": "'\\app\\Listeners\\CheckoutableListener.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\RateLimiter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cache\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\FileStore.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RetrievesMultipleKeys.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cache\\Store.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cache\\LockProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cache\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\psr\\simple-cache\\src\\CacheInterface.php',", "is_string": true}, {"message": "'\\routes\\api.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResourceRegistrar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\PendingResourceRegistration.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Pluralizer.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\InflectorFactory.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Language.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\English\\InflectorFactory.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\GenericLanguageInflectorFactory.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\LanguageInflectorFactory.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\English\\Rules.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Ruleset.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Transformations.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\WordInflector.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\English\\Inflectible.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Transformation.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Pattern.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Patterns.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\English\\Uninflected.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Substitutions.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Substitution.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Word.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Inflector.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\CachedWordInflector.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\RulesetInflector.php',", "is_string": true}, {"message": "'\\routes\\web\\hardware.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Support\\ClosureScope.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Support\\ReflectionClosure.php',", "is_string": true}, {"message": "'\\routes\\web\\models.php',", "is_string": true}, {"message": "'\\routes\\web\\accessories.php',", "is_string": true}, {"message": "'\\routes\\web\\licenses.php',", "is_string": true}, {"message": "'\\routes\\web\\locations.php',", "is_string": true}, {"message": "'\\routes\\web\\consumables.php',", "is_string": true}, {"message": "'\\routes\\web\\fields.php',", "is_string": true}, {"message": "'\\routes\\web\\components.php',", "is_string": true}, {"message": "'\\routes\\web\\users.php',", "is_string": true}, {"message": "'\\routes\\web\\kits.php',", "is_string": true}, {"message": "'\\routes\\web.php',", "is_string": true}, {"message": "'\\app\\Livewire\\Importer.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Component.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Auth\\Access\\AuthorizesRequests.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Concerns\\InteractsWithProperties.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportEvents\\HandlesEvents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportRedirects\\HandlesRedirects.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportStreaming\\HandlesStreaming.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportAttributes\\HandlesAttributes.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportValidation\\HandlesValidation.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportFormObjects\\HandlesFormObjects.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportJsEvaluation\\HandlesJsEvaluation.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\HandlesDisablingBackButtonCache.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\HealthController.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\DashboardController.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\Controller.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bus\\DispatchesJobs.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\GroupsController.php',", "is_string": true}, {"message": "'\\routes\\scim.php',", "is_string": true}, {"message": "'\\vendor\\arietimmerman\\laravel-scim-server\\src\\RouteProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewFinderInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesFragments.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesLayouts.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesLoops.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesStacks.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesTranslations.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\View\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewName.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\App.php',", "is_string": true}, {"message": "'\\resources\\macros\\macros.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\FormFacade.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\HtmlServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\FormBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\Componentable.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\HtmlBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\FileSessionHandler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Session\\Session.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Breadcrumbs.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Manager.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Trail.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Registrar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Pipeline\\Pipeline.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\TrustProxies.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\NoSessionStore.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\Concerns\\ExcludesPaths.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\MaintenanceModeManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\FileBasedMaintenanceMode.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Foundation\\MaintenanceMode.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SymfonySessionDecorator.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\Session\\SessionInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ViewErrorBag.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckForSetup.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsWhereDateClauses.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ExplainsQueries.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Database\\Query\\Builder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Database\\Eloquent\\Builder.php',", "is_string": true}, {"message": "'\\app\\Models\\Company.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectorInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Events\\StatementPrepared.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Events\\QueryExecuted.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\QueryRecorder\\Query.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Collection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\InteractsWithDictionary.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Queue\\QueueableCollection.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckForDebug.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\CreatesUserProviders.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\StatefulGuard.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Guard.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\SupportsBasicAuth.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\UserProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Hashing\\HashServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Hashing\\HashManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Hashing\\Hasher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Timebox.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\CookieJar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cookie\\QueueingFactory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cookie\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Events\\Authenticated.php',", "is_string": true}, {"message": "'\\app\\Models\\Group.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\InteractsWithPivotTable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\JoinClause.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\TrimStrings.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\SecurityHeaders.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\PreventBackHistory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php',", "is_string": true}, {"message": "'\\vendor\\fruitcake\\php-cors\\src\\CorsService.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\IpUtils.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\LaravelDebugbar.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DebugBar.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\Support\\RequestIdGenerator.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\RequestIdGeneratorInterface.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\Storage\\FilesystemStorage.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\Storage\\StorageInterface.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\PhpInfoCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\DataCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\HasDataFormatter.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\HasXdebugLinks.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\DataCollectorInterface.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\Renderable.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\MessagesCollector.php',", "is_string": true}, {"message": "'\\vendor\\psr\\log\\src\\AbstractLogger.php',", "is_string": true}, {"message": "'\\vendor\\psr\\log\\src\\LoggerTrait.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\MessagesAggregateInterface.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\AssetProvider.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\TimeDataCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\DataFormatter.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\DataFormatterInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\error-handler\\ErrorRenderer\\FileLinkFormatter.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\error-handler\\ErrorRenderer\\ErrorRendererInterface.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\MemoryCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\ExceptionsCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\LaravelCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\EventCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataFormatter\\SimpleFormatter.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\ViewCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\RouteCollector.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\StreamHandler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\AbstractProcessingHandler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\AbstractHandler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\ProcessableHandlerTrait.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\FormattableHandlerTrait.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\ProcessableHandlerInterface.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\FormattableHandlerInterface.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Utils.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Formatter\\LineFormatter.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Formatter\\NormalizerFormatter.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Formatter\\FormatterInterface.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\QueryCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\PDO\\PDOCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataFormatter\\QueryFormatter.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\ObjectCountCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\LivewireCollector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\MailServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\Bridge\\Symfony\\SymfonyMailCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\LogsCollector.php',", "is_string": true}, {"message": "'\\vendor\\psr\\log\\src\\LogLevel.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\FilesCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\MultiAuthCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\GateCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\JavascriptRenderer.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\JavascriptRenderer.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Events\\Routing.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\routing\\Route.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\routing\\RouteCompiler.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\routing\\RouteCompilerInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\routing\\CompiledRoute.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\UriValidator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\ValidatorInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\MethodValidator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\SchemeValidator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\HostValidator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteParameterBinder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Events\\RouteMatched.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Contracts\\ControllerDispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\MiddlewareNameResolver.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\SortedMiddleware.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\EncryptCookies.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\VerifyCsrfToken.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckLocale.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckUserIsActivated.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckForTwoFactor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\AssetCountForSidebar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Session\\Middleware\\AuthenticatesSessions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Middleware\\AuthenticatesRequests.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\BreadcrumbsMiddleware.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\Encrypter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Encryption\\Encrypter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Encryption\\StringEncrypter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Encryption\\DecryptException.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\CookieValuePrefix.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php',", "is_string": true}, {"message": "'\\app\\Helpers\\Helper.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\ApiTokenCookieFactory.php',", "is_string": true}, {"message": "'\\app\\Models\\Statuslabel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\BelongsTo.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\ComparesRelatedModels.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\SupportsDefaultModels.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteSignatureParameters.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Support\\ClosureStream.php',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n            $trail->parent('hardware.index')\r\n                ->push(\\trans('general.bulkaudit'), \\route('asset.import-history'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n            $trail->parent('hardware.index')\r\n                ->push('Quickscan Checkin', \\route('hardware/quickscancheckin'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n            $trail->parent('hardware.index')\r\n                ->push(\\trans('admin/hardware/general.requested'), \\route('assets.requested'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n            $trail->parent('hardware.index')\r\n                ->push(\\trans_choice('general.audit_due_days', \\App\\Models\\Setting::getSettings()->audit_warning_days, ['days' => \\App\\Models\\Setting::getSettings()->audit_warning_days]), \\route('assets.audit.due'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n            $trail->parent('hardware.index')\r\n                ->push(\\trans_choice('general.checkin_due_days', \\App\\Models\\Setting::getSettings()->due_checkin_days, ['days' => \\App\\Models\\Setting::getSettings()->due_checkin_days]), \\route('assets.audit.due'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, \\App\\Models\\Asset $asset) =>\r\n            $trail->parent('hardware.show', $asset)\r\n                ->push(\\trans('general.audit'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n                $trail->parent('hardware.index')\r\n                ->push(\\trans('general.import-history'), \\route('asset.import-history'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, \\App\\Models\\Asset $asset) =>\r\n            $trail->parent('hardware.show', $asset)\r\n                ->push(\\trans('admin/hardware/general.checkout'), \\route('hardware.index'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, \\App\\Models\\Asset $asset) =>\r\n        $trail->parent('hardware.show', $asset)\r\n            ->push(\\trans('admin/hardware/general.checkin'), \\route('hardware.index'))\r\n        ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n            $trail->parent('hardware.index')\r\n                ->push(\\trans('admin/hardware/general.bulk_checkout'), \\route('hardware.index'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('models.index')\r\n            ->push(\\trans('admin/models/table.clone'), \\route('models.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, \\App\\Models\\License $license) =>\r\n        $trail->parent('licenses.show', $license)\r\n            ->push(\\trans('general.checkout'), \\route('licenses.checkout', $license))\r\n        ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, \\App\\Models\\LicenseSeat $licenseSeat) =>\r\n        $trail->parent('licenses.show', $licenseSeat->license)\r\n            ->push(\\trans('general.checkin'), \\route('licenses.checkin', $licenseSeat))\r\n        ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.backups'), \\route('kits.licenses.edit'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, \\App\\Models\\PredefinedKit $kit) =>\r\n        $trail->parent('kits.show', $kit)\r\n            ->push(\\trans('general.checkout'), \\route('kits.checkout.show', $kit))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.general_title'), \\route('settings.general.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.branding_title'), \\route('settings.branding.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.security_title'), \\route('settings.security.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.localization_title'), \\route('settings.localization.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.alert_title'), \\route('settings.alerts.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.webhook_title'), \\route('settings.slack.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.asset_tag_title'), \\route('settings.asset_tags.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.labels_title'), \\route('settings.labels.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.ldap_ad'), \\route('settings.ldap.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.php_info'), \\route('settings.phpinfo.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.oauth'), \\route('settings.oauth.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.google_login'), \\route('settings.google.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.purge'), \\route('settings.purge.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.login'), \\route('settings.logins.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.saml_title'), \\route('settings.saml.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n            $trail->parent('settings.index')\r\n                ->push(\\trans('admin/settings/general.backups'), \\route('settings.backups.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n    $trail->parent('home')\r\n        ->push(\\trans('general.import'), \\route('imports.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n                $trail->parent('home')\r\n            ->push(\\trans('general.editprofile'), \\route('profile'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.changepassword'), \\route('account.password.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.manage_api_keys'), \\route('user.api'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.viewassets'), \\route('view-assets'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.requested_assets_menu'), \\route('account.requested'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.requestable_items'), \\route('requestable-assets'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.accept_assets_menu'), \\route('account.accept'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, $id) =>\r\n        $trail->parent('account.accept')\r\n            ->push(\\trans('general.accept_item'), \\route('account.accept.item', $id))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.audit_report'), \\route('reports.audit'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.depreciation_report'), \\route('reports/depreciation'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.depreciation_report'), \\route('reports.audit'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.asset_maintenance_report'), \\route('reports/asset_maintenances'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.asset_maintenance_report'), \\route('reports/export/asset_maintenances'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.license_report'), \\route('reports/licenses'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.custom_report'), \\route('reports/custom'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, \\App\\Models\\ReportTemplate $reportTemplate) =>\r\n                $trail->parent('reports/custom')\r\n                    ->push($reportTemplate->name, null)\r\n                    ->push(\\trans('general.customize_report'), '')',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, \\App\\Models\\ReportTemplate $reportTemplate) =>\r\n                $trail->parent('reports/custom')\r\n                    ->push($reportTemplate->name, \\route('report-templates.show', $reportTemplate))\r\n                    ->push(\\trans('general.customize_report'), '')',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.activity_report'), \\route('reports.activity'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.unaccepted_asset_report'), \\route('reports/unaccepted_assets'))',", "is_string": true}, {"message": "'\\app\\Models\\LicenseSeat.php',", "is_string": true}, {"message": "'\\app\\Models\\CompanyableChildTrait.php',", "is_string": true}, {"message": "'\\app\\Models\\ICompanyableChild.php',", "is_string": true}, {"message": "'\\app\\Models\\CompanyableChildScope.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\View\\Engine.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Events\\PreparingResponse.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\Response.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\ResponseHeaderBag.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\ResponseTrait.php',", "is_string": true}, {"message": "'\\storage\\framework\\views\\634f41542304d78abb74aff576389081.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\general.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteUrlGenerator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\AnonymousComponent.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ComponentSlot.php',", "is_string": true}, {"message": "'\\storage\\framework\\views\\e42f7a353f1c45b64eeb8c8510b72bd0.php',", "is_string": true}, {"message": "'\\app\\Helpers\\IconHelper.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\DebugBarVarDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\Data.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\Stub.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\VarDumper\\DebugBarHtmlDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\Cursor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Events\\GateEvaluated.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Response.php',", "is_string": true}, {"message": "'\\storage\\framework\\views\\3cc4bcaaf43f1522cca5afa49210928a.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Mix.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\HtmlString.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\table.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\admin\\hardware\\general.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\admin\\hardware\\message.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\button.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\admin\\licenses\\general.php',", "is_string": true}, {"message": "'\\storage\\framework\\views\\603bcce3082fdd3035041e8b49695eb1.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Request.php',", "is_string": true}, {"message": "'\\app\\Models\\ConsumableAssignment.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasMany.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\SupportsInverseRelations.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Expression.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Database\\Query\\Expression.php',", "is_string": true}, {"message": "'\\app\\Models\\AccessoryCheckout.php',", "is_string": true}, {"message": "'\\app\\Models\\AssetModel.php',", "is_string": true}, {"message": "'\\app\\Http\\Traits\\TwoColumnUniqueUndeletedTrait.php',", "is_string": true}, {"message": "'\\app\\Presenters\\UserPresenter.php',", "is_string": true}, {"message": "'\\app\\Presenters\\Presenter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Storage.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Filesystem\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\UnixVisibility\\PortableVisibilityConverter.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\UnixVisibility\\VisibilityConverter.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem-local\\LocalFilesystemAdapter.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\FilesystemAdapter.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\ChecksumProvider.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\PathPrefixer.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem-local\\FallbackMimeTypeDetector.php',", "is_string": true}, {"message": "'\\vendor\\league\\mime-type-detection\\src\\MimeTypeDetector.php',", "is_string": true}, {"message": "'\\vendor\\league\\mime-type-detection\\src\\FinfoMimeTypeDetector.php',", "is_string": true}, {"message": "'\\vendor\\league\\mime-type-detection\\src\\ExtensionLookup.php',", "is_string": true}, {"message": "'\\vendor\\league\\mime-type-detection\\src\\GeneratedExtensionToMimeTypeMap.php',", "is_string": true}, {"message": "'\\vendor\\league\\mime-type-detection\\src\\ExtensionToMimeTypeMap.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\Visibility.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\LocalFilesystemAdapter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Filesystem\\Cloud.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Filesystem\\Filesystem.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\Filesystem.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\CalculateChecksumFromStream.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\FilesystemOperator.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\FilesystemReader.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\FilesystemWriter.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\Config.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\WhitespacePathNormalizer.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\PathNormalizer.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\admin\\custom_fields\\general.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Crumb.php',", "is_string": true}, {"message": "'\\storage\\framework\\views\\3b1b16c575e20bc87c056cbfccd78ad8.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\MessageBag.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\MessageBag.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\MessageProvider.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\datepicker.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\validation.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Session.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Events\\ResponsePrepared.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\SymfonyHttpDriver.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\HttpDriverInterface.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\SessionCollector.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\uid\\Ulid.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\uid\\AbstractUid.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\uid\\HashableInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\uid\\TimeBasedUidInterface.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\RequestCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\Support\\Clockwork\\ClockworkCollector.php',", "is_string": true}], "count": 1060}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"mgpiattos\"\n  \"user\" => array:43 [\n    \"id\" => 1\n    \"email\" => \"<EMAIL>\"\n    \"activated\" => 1\n    \"created_by\" => null\n    \"activated_at\" => null\n    \"last_login\" => \"2025-08-16 08:39:49\"\n    \"first_name\" => \"<PERSON>\"\n    \"last_name\" => \"Piattos\"\n    \"created_at\" => \"2025-08-16T08:39:28.000000Z\"\n    \"updated_at\" => \"2025-08-16T08:39:49.000000Z\"\n    \"deleted_at\" => null\n    \"website\" => null\n    \"country\" => null\n    \"gravatar\" => null\n    \"location_id\" => null\n    \"phone\" => null\n    \"jobtitle\" => null\n    \"manager_id\" => null\n    \"employee_num\" => null\n    \"avatar\" => null\n    \"username\" => \"mgpiattos\"\n    \"notes\" => null\n    \"company_id\" => null\n    \"ldap_import\" => 0\n    \"locale\" => \"en-US\"\n    \"show_in_list\" => 1\n    \"two_factor_enrolled\" => 0\n    \"two_factor_optin\" => 0\n    \"department_id\" => null\n    \"address\" => null\n    \"city\" => null\n    \"state\" => null\n    \"zip\" => null\n    \"skin\" => null\n    \"remote\" => 0\n    \"start_date\" => null\n    \"end_date\" => null\n    \"scim_externalid\" => null\n    \"autoassign_licenses\" => 1\n    \"vip\" => 0\n    \"enable_sounds\" => 0\n    \"enable_confetti\" => 0\n    \"groups\" => []\n  ]\n]", "api": "null"}, "names": "web: mgpiattos"}, "gate": {"count": 54, "messages": [{"message": "[\n  ability => create,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.621217, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\License,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\License]\n]", "message_html": "<pre class=sf-dump id=sf-dump-671812832 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\License</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\License</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\License]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-671812832\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.622594, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Accessory,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Accessory]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Accessory</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Accessory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Accessory]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.622913, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Consumable,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Consumable]\n]", "message_html": "<pre class=sf-dump id=sf-dump-902494577 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Consumable</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Consumable</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\Consumable]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-902494577\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.623242, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Component,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Component]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1238124045 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Component</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Component</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Component]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1238124045\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.623498, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1516902200 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1516902200\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.623727, "xdebug_link": null}, {"message": "[\n  ability => index,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>index App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.646633, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\License,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\License]\n]", "message_html": "<pre class=sf-dump id=sf-dump-761896759 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\License</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\License</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\License]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-761896759\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.64804, "xdebug_link": null}, {"message": "[\n  ability => index,\n  target => App\\Models\\Accessory,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Accessory]\n]", "message_html": "<pre class=sf-dump id=sf-dump-289945668 data-indent-pad=\"  \"><span class=sf-dump-note>index App\\Models\\Accessory</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Accessory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Accessory]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-289945668\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.64917, "xdebug_link": null}, {"message": "[\n  ability => index,\n  target => App\\Models\\Consumable,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Consumable]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1286498483 data-indent-pad=\"  \"><span class=sf-dump-note>index App\\Models\\Consumable</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Consumable</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\Consumable]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1286498483\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.650095, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Component,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Component]\n]", "message_html": "<pre class=sf-dump id=sf-dump-303234132 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Component</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Component</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Component]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-303234132\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.650764, "xdebug_link": null}, {"message": "[\n  ability => index,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1119728286 data-indent-pad=\"  \"><span class=sf-dump-note>index App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1119728286\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.651435, "xdebug_link": null}, {"message": "[ability => admin, target => null, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1596284332 data-indent-pad=\"  \"><span class=sf-dump-note>admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1596284332\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.65206, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.652238, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\License,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\License]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\License</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\License</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\License]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.652896, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Accessory,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Accessory]\n]", "message_html": "<pre class=sf-dump id=sf-dump-667448200 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Accessory</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Accessory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Accessory]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-667448200\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.653521, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Consumable,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Consumable]\n]", "message_html": "<pre class=sf-dump id=sf-dump-334542528 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Consumable</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Consumable</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\Consumable]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-334542528\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.654129, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Component,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Component]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1953172810 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Component</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Component</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Component]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1953172810\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.655118, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-555966010 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-555966010\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.656475, "xdebug_link": null}, {"message": "[ability => admin, target => null, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-714684075 data-indent-pad=\"  \"><span class=sf-dump-note>admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-714684075\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.658153, "xdebug_link": null}, {"message": "[\n  ability => superadmin,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1624015449 data-indent-pad=\"  \"><span class=sf-dump-note>superadmin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">superadmin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1624015449\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.678781, "xdebug_link": null}, {"message": "[\n  ability => viewRequestable,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1344315279 data-indent-pad=\"  \"><span class=sf-dump-note>viewRequestable App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">viewRequestable</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1344315279\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.693983, "xdebug_link": null}, {"message": "[\n  ability => self.profile,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-597304067 data-indent-pad=\"  \"><span class=sf-dump-note>self.profile </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">self.profile</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-597304067\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.696198, "xdebug_link": null}, {"message": "[\n  ability => self.api,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-302855542 data-indent-pad=\"  \"><span class=sf-dump-note>self.api </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">self.api</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-302855542\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.698147, "xdebug_link": null}, {"message": "[\n  ability => superadmin,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1488427747 data-indent-pad=\"  \"><span class=sf-dump-note>superadmin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">superadmin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1488427747\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.699903, "xdebug_link": null}, {"message": "[ability => admin, target => null, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-659767205 data-indent-pad=\"  \"><span class=sf-dump-note>admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-659767205\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.70142, "xdebug_link": null}, {"message": "[\n  ability => index,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-363756391 data-indent-pad=\"  \"><span class=sf-dump-note>index App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-363756391\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.702348, "xdebug_link": null}, {"message": "[\n  ability => audit,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1785877673 data-indent-pad=\"  \"><span class=sf-dump-note>audit App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">audit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1785877673\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.711477, "xdebug_link": null}, {"message": "[\n  ability => checkin,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1782924241 data-indent-pad=\"  \"><span class=sf-dump-note>checkin App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">checkin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1782924241\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.712216, "xdebug_link": null}, {"message": "[\n  ability => checkin,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1413368221 data-indent-pad=\"  \"><span class=sf-dump-note>checkin App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">checkin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1413368221\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.712888, "xdebug_link": null}, {"message": "[\n  ability => checkout,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1221727168 data-indent-pad=\"  \"><span class=sf-dump-note>checkout App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">checkout</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1221727168\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.713146, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2091072918 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2091072918\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.713409, "xdebug_link": null}, {"message": "[ability => admin, target => null, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1235251813 data-indent-pad=\"  \"><span class=sf-dump-note>admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1235251813\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.713647, "xdebug_link": null}, {"message": "[\n  ability => audit,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-7377856 data-indent-pad=\"  \"><span class=sf-dump-note>audit App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">audit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-7377856\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.713846, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\License,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\License]\n]", "message_html": "<pre class=sf-dump id=sf-dump-689976268 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\License</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\License</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\License]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-689976268\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.714057, "xdebug_link": null}, {"message": "[\n  ability => index,\n  target => App\\Models\\Accessory,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Accessory]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>index App\\Models\\Accessory</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Accessory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Accessory]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.714675, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Consumable,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Consumable]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2049730760 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Consumable</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Consumable</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\Consumable]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2049730760\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.715254, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Component,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Component]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1685728538 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Component</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Component</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Component]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1685728538\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.715812, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\PredefinedKit,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PredefinedKit]\n]", "message_html": "<pre class=sf-dump id=sf-dump-154929452 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\PredefinedKit</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\PredefinedKit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\PredefinedKit]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-154929452\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.71639, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1412659962 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1412659962\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.71699, "xdebug_link": null}, {"message": "[ability => import, target => null, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-479809954 data-indent-pad=\"  \"><span class=sf-dump-note>import </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">import</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-479809954\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.717599, "xdebug_link": null}, {"message": "[\n  ability => backend.interact,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2090207912 data-indent-pad=\"  \"><span class=sf-dump-note>backend.interact </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">backend.interact</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2090207912\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.718215, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\CustomField,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\CustomField]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2057152016 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\CustomField</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\CustomField</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\CustomField]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2057152016\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.719096, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Statuslabel,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Statuslabel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1133926328 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Statuslabel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Statuslabel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\Statuslabel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1133926328\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.719824, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\AssetModel,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AssetModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1199740038 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\AssetModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\AssetModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\AssetModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1199740038\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.720082, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Category,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Category]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1963369487 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Category</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"19 characters\">App\\Models\\Category</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; App\\Models\\Category]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1963369487\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.720299, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Manufacturer,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Manufacturer]\n]", "message_html": "<pre class=sf-dump id=sf-dump-731850128 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Manufacturer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Manufacturer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\Manufacturer]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-731850128\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.720505, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Supplier,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Supplier]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2056341219 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Supplier</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"19 characters\">App\\Models\\Supplier</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; App\\Models\\Supplier]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2056341219\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.720702, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Department,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Department]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1313245620 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Department</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Department</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\Department]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1313245620\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.720902, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Location,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Location]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1365361588 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Location</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"19 characters\">App\\Models\\Location</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; App\\Models\\Location]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1365361588\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.721109, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Company,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Company]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1320636146 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Company</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Company</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Company]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1320636146\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.721711, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Depreciation,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Depreciation]\n]", "message_html": "<pre class=sf-dump id=sf-dump-37449174 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Depreciation</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Depreciation</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\Depreciation]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-37449174\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.722353, "xdebug_link": null}, {"message": "[\n  ability => reports.view,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1932686316 data-indent-pad=\"  \"><span class=sf-dump-note>reports.view </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">reports.view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1932686316\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.722613, "xdebug_link": null}, {"message": "[\n  ability => viewRequestable,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-335862731 data-indent-pad=\"  \"><span class=sf-dump-note>viewRequestable App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">viewRequestable</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-335862731\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.724052, "xdebug_link": null}]}, "session": {"url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/_debugbar/open?id=01K2S0K4G01RDMCKNMBBMV1S3M&op=get\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_token": "XcFb6uBj6sHVSCD8PzSZfpkew7wBekXM9V2bT1eX", "backUrl": "http://127.0.0.1:8000/login", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "$2y$10$2Y6DCYiWr0S5QSGbJatyfuYNCX1.Ib9MeBtmJ22fHJVGhcCwvfQ3G"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000", "action_name": "home", "controller_action": "\\App\\Http\\Controllers\\DashboardController@index", "uri": "GET /", "controller": "\\App\\Http\\Controllers\\DashboardController@index<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=27\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=27\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/DashboardController.php:27-53</a>", "middleware": "web, auth, breadcrumbs", "duration": "949ms", "peak_memory": "42MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1606385421 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1606385421\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1154430773 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1154430773\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1842096089 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Microsoft Edge&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1078 characters\">csrftoken=eRZeQ4hlaHT8dltJRqLktclcixjXegkX; snipeit_session=Q20TsmwTLpe7V4NLPLzu7UJvMbqL6bJ6wfpUffVy; snipeit_passport_token=eyJpdiI6ImFJUFU1aWJTZGI1ZDIyVU5MNUltM3c9PSIsInZhbHVlIjoiRC9FR0JwZDRXdUpyZk95YlM4OXFTek9vOWdkSC9Gam5ZWFJHRUZvWkxzYU9OdW96ckhnbG1yc3FWeFdKQllBZEVUa0QzK0RaUHhLT0lST2NxbU91R09QZjlCTk5IRis4TzNMUjJoaXd3ZVFnSG5zQWtkcFhQL09SRGUrb2dhZjhudUJPNWdKdVNHUDYzUGNoTDV2TTV5Y0lIYlY4ZEc2MDErY2c4eTRkZlFLYnR3QUxRdUZ3M0dSRFRrdGxCNU1oNG51U1pOUDI0MmMycmhjVU0wcHZqZ3VmMm16dUdKMlZTWXdPN09xbjFSYWtEZjZ6SFU3c0xuQ2NReW9BL0owcTdZaVpPbUxJYi9Odk9GclQvMS8wNHFEY3Y3MzVxSTFZeCtVNlBncTFmeTR2TUFZdU1iUWsvWE5LaTlFS3FOVmgiLCJtYWMiOiI2ZDBiYTA2ZTE4YmFhYTViYzJjY2U4MDJhOTlmM2Q4NzU3MWIwNjkxMDM2ZDY5MjE5NTY3OGJjZDRhODQxODY3IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IndmTWoySHlwOHRXYXh6aW9WR0hUNXc9PSIsInZhbHVlIjoiMVpOWFlLY3ZRenFudGdIWlU5aSt3RVZlR0ErTnlFRzVkVlJKRnVLMkx1ZWpqeURBbk1Mc1BPUEJhb1IyRkxJUEZiOVk1aWNJZTZwMXN6aGlnUU5Bb2hoTmo1Q0p3UElQNDNxcm1ZN3JESXppbGVvM01SK1FkZXZNVGFPTnNuK2MiLCJtYWMiOiJkNGM3ZTk3NzhkN2E4MWM4MjlkOGNjMjZmZDA3ODkwN2ZlMDgyOWRhYmU0NTQ5YzZhYzVlMjg3NmQ3NWVkNTdmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1842096089\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1006738513 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>csrftoken</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>snipeit_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>snipeit_passport_token</span>\" => \"<span class=sf-dump-str title=\"187 characters\">eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOjEsImNzcmYiOiJYY0ZiNnVCajZzSFZTQ0Q4UHpTWmZwa2V3N3dCZWtYTTlWMmJUMWVYIiwiZXhwaXJ5IjoxNzU2MDUzNjIyfQ.DwSs2vXkPVgt3Y4m-gUGeSaNd-O5rf7-ZEpCsxdIztQ</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XcFb6uBj6sHVSCD8PzSZfpkew7wBekXM9V2bT1eX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1006738513\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 16 Aug 2025 08:40:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-clockwork-id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">01K2S0KX77ERB9R2Q7499NZDKW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-clockwork-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-clockwork-path</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">_debugbar/clockwork/</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1392801072 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"73 characters\">http://127.0.0.1:8000/_debugbar/open?id=01K2S0K4G01RDMCKNMBBMV1S3M&amp;op=get</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XcFb6uBj6sHVSCD8PzSZfpkew7wBekXM9V2bT1eX</span>\"\n  \"<span class=sf-dump-key>backUrl</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$2Y6DCYiWr0S5QSGbJatyfuYNCX1.Ib9MeBtmJ22fHJVGhcCwvfQ3G</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1392801072\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000", "action_name": "home", "controller_action": "\\App\\Http\\Controllers\\DashboardController@index"}, "badge": null}, "clockwork": {"getData": [], "postData": [], "headers": {"host": ["127.0.0.1:8000"], "connection": ["keep-alive"], "cache-control": ["max-age=0"], "sec-ch-ua": ["\"Not;A=Brand\";v=\"99\", \"Microsoft Edge\";v=\"139\", \"Chromium\";v=\"139\""], "sec-ch-ua-mobile": ["?0"], "sec-ch-ua-platform": ["\"Windows\""], "upgrade-insecure-requests": ["1"], "user-agent": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"], "accept": ["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"], "sec-fetch-site": ["same-origin"], "sec-fetch-mode": ["navigate"], "sec-fetch-user": ["?1"], "sec-fetch-dest": ["document"], "referer": ["http://127.0.0.1:8000/login"], "accept-encoding": ["gzip, deflate, br, zstd"], "accept-language": ["en-US,en;q=0.9"], "cookie": ["csrftoken=eRZeQ4hlaHT8dltJRqLktclcixjXegkX; snipeit_session=Q20TsmwTLpe7V4NLPLzu7UJvMbqL6bJ6wfpUffVy; snipeit_passport_token=eyJpdiI6ImFJUFU1aWJTZGI1ZDIyVU5MNUltM3c9PSIsInZhbHVlIjoiRC9FR0JwZDRXdUpyZk95YlM4OXFTek9vOWdkSC9Gam5ZWFJHRUZvWkxzYU9OdW96ckhnbG1yc3FWeFdKQllBZEVUa0QzK0RaUHhLT0lST2NxbU91R09QZjlCTk5IRis4TzNMUjJoaXd3ZVFnSG5zQWtkcFhQL09SRGUrb2dhZjhudUJPNWdKdVNHUDYzUGNoTDV2TTV5Y0lIYlY4ZEc2MDErY2c4eTRkZlFLYnR3QUxRdUZ3M0dSRFRrdGxCNU1oNG51U1pOUDI0MmMycmhjVU0wcHZqZ3VmMm16dUdKMlZTWXdPN09xbjFSYWtEZjZ6SFU3c0xuQ2NReW9BL0owcTdZaVpPbUxJYi9Odk9GclQvMS8wNHFEY3Y3MzVxSTFZeCtVNlBncTFmeTR2TUFZdU1iUWsvWE5LaTlFS3FOVmgiLCJtYWMiOiI2ZDBiYTA2ZTE4YmFhYTViYzJjY2U4MDJhOTlmM2Q4NzU3MWIwNjkxMDM2ZDY5MjE5NTY3OGJjZDRhODQxODY3IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IndmTWoySHlwOHRXYXh6aW9WR0hUNXc9PSIsInZhbHVlIjoiMVpOWFlLY3ZRenFudGdIWlU5aSt3RVZlR0ErTnlFRzVkVlJKRnVLMkx1ZWpqeURBbk1Mc1BPUEJhb1IyRkxJUEZiOVk1aWNJZTZwMXN6aGlnUU5Bb2hoTmo1Q0p3UElQNDNxcm1ZN3JESXppbGVvM01SK1FkZXZNVGFPTnNuK2MiLCJtYWMiOiJkNGM3ZTk3NzhkN2E4MWM4MjlkOGNjMjZmZDA3ODkwN2ZlMDgyOWRhYmU0NTQ5YzZhYzVlMjg3NmQ3NWVkNTdmIiwidGFnIjoiIn0%3D"]}, "cookies": {"csrftoken": null, "snipeit_session": null, "snipeit_passport_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOjEsImNzcmYiOiJYY0ZiNnVCajZzSFZTQ0Q4UHpTWmZwa2V3N3dCZWtYTTlWMmJUMWVYIiwiZXhwaXJ5IjoxNzU2MDUzNjIyfQ.DwSs2vXkPVgt3Y4m-gUGeSaNd-O5rf7-ZEpCsxdIztQ", "XSRF-TOKEN": "XcFb6uBj6sHVSCD8PzSZfpkew7wBekXM9V2bT1eX"}, "uri": "/", "method": "GET", "responseStatus": 200, "sessionData": {"url": [], "_previous": {"url": "http://127.0.0.1:8000/_debugbar/open?id=01K2S0K4G01RDMCKNMBBMV1S3M&op=get"}, "_flash": {"old": [], "new": []}, "_token": "XcFb6uBj6sHVSCD8PzSZfpkew7wBekXM9V2bT1eX", "backUrl": "http://127.0.0.1:8000/login", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": 1, "password_hash_web": "$2y$10$2Y6DCYiWr0S5QSGbJatyfuYNCX1.Ib9MeBtmJ22fHJVGhcCwvfQ3G"}}}