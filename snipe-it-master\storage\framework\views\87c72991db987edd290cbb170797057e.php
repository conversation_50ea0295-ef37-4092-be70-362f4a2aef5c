


<?php $__env->startSection('inputFields'); ?>

<?php echo $__env->make('partials.forms.edit.name', ['translated_name' => trans('admin/models/table.name'), 'required' => 'true'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('partials.forms.edit.category-select', ['translated_name' => trans('admin/categories/general.category_name'), 'fieldname' => 'category_id', 'required' => 'true', 'category_type' => 'asset'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('partials.forms.edit.manufacturer-select', ['translated_name' => trans('general.manufacturer'), 'fieldname' => 'manufacturer_id'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('partials.forms.edit.model_number', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('partials.forms.edit.depreciation', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('partials.forms.edit.minimum_quantity', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<!-- EOL -->

<div class="form-group <?php echo e($errors->has('eol') ? ' has-error' : ''); ?>">
    <label for="eol" class="col-md-3 control-label"><?php echo e(trans('general.eol')); ?></label>
    <div class="col-md-3 col-sm-4 col-xs-7">
        <div class="input-group">
            <input class="form-control" type="text" name="eol" id="eol" value="<?php echo e(old('eol', isset($item->eol)) ? $item->eol : ''); ?>" />
            <span class="input-group-addon">
                <?php echo e(trans('general.months')); ?>

            </span>
        </div>
    </div>
    <div class="col-md-9 col-md-offset-3">
        <?php echo $errors->first('eol', '<span class="alert-msg" aria-hidden="true"><br><i class="fas fa-times"></i> :message</span>'); ?>

    </div>
</div>

<!-- Warranty Expiration (Customized Field) -->
<div class="form-group <?php echo e($errors->has('model_warranty') ? ' has-error' : ''); ?>">
    <label for="model_warranty" class="col-md-3 control-label"><?php echo e(trans('Warranty')); ?></label>
    <div class="col-md-3 col-sm-4 col-xs-7">
        <div class="input-group">
            <input class="form-control" type="text" 
                   name="model_warranty" 
                   id="model_warranty" 
                   value="<?php echo e(old('model_warranty', isset($item->model_warranty) ? $item->model_warranty : '')); ?>" />
            <span class="input-group-addon">
                <?php echo e(trans('general.months')); ?>

            </span>
        </div>
    </div>
    <div class="col-md-9 col-md-offset-3">
        <?php echo $errors->first('model_warranty', '<span class="alert-msg" aria-hidden="true"><br><i class="fas fa-times"></i> :message</span>'); ?>

    </div>
</div>


<!-- Custom Fieldset -->
<!-- If $item->id is null we are cloning the model and we need the $model_id variable -->
<?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('custom-field-set-default-values-for-model', ["model_id" => $item->id ?? $model_id ?? null]);

$__html = app('livewire')->mount($__name, $__params, 'lw-2737017610-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

<?php echo $__env->make('partials.forms.edit.notes', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('partials.forms.edit.requestable', ['requestable_text' => trans('admin/models/general.requestable')], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('partials.forms.edit.image-upload', ['image_path' => app('models_upload_path')], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>


<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts/edit-form', [
    'createText' => trans('admin/models/table.create') ,
    'updateText' => trans('admin/models/table.update'),
    'topSubmit' => true,
    'helpPosition' => 'right',
    'helpText' => trans('admin/models/general.about_models_text'),
    'formAction' => (isset($item->id)) ? route('models.update', ['model' => $item->id]) : route('models.store'),
], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\Snipe-IT-AMS-Group7\snipe-it-master\resources\views/models/edit.blade.php ENDPATH**/ ?>