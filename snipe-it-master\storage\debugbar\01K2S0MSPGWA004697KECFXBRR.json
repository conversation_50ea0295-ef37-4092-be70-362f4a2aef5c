{"__meta": {"id": "01K2S0MSPGWA004697KECFXBRR", "datetime": "2025-08-16 08:41:22", "utime": **********.897914, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[08:41:22] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 311", "message_html": null, "is_string": false, "label": "warning", "time": **********.850957, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 34, "start": **********.043639, "end": **********.897957, "duration": 0.8543181419372559, "duration_str": "854ms", "measures": [{"label": "Booting", "start": **********.043639, "relative_start": 0, "end": **********.553815, "relative_end": **********.553815, "duration": 0.****************, "duration_str": "510ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.553849, "relative_start": 0.****************, "end": **********.897961, "relative_end": 3.814697265625e-06, "duration": 0.*****************, "duration_str": "344ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.675087, "relative_start": 0.****************, "end": **********.691069, "relative_end": **********.691069, "duration": 0.*****************, "duration_str": "15.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "select count(*) as aggregate from `assets` where `assets`.`deleted_at` is null", "start": **********.70219, "relative_start": 0.****************, "end": **********.70344, "relative_end": **********.70344, "duration": 0.0012500286102294922, "duration_str": "1.25ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets...", "start": **********.7105298, "relative_start": 0.****************, "end": **********.71167, "relative_end": **********.71167, "duration": 0.0011401176452636719, "duration_str": "1.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`assigned_to` is null and exists (select *...", "start": **********.713867, "relative_start": 0.6702280044555664, "end": **********.714607, "relative_end": **********.714607, "duration": 0.00074005126953125, "duration_str": "740μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assigned_to` > ? and `assets`.`deleted_at` is null", "start": **********.716429, "relative_start": 0.6727900505065918, "end": **********.716999, "relative_end": **********.716999, "duration": 0.0005700588226318359, "duration_str": "570μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets...", "start": **********.718915, "relative_start": 0.6752760410308838, "end": **********.719585, "relative_end": **********.719585, "duration": 0.0006699562072753906, "duration_str": "670μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets...", "start": **********.721918, "relative_start": 0.6782791614532471, "end": **********.722598, "relative_end": **********.722598, "duration": 0.0006799697875976562, "duration_str": "680μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets...", "start": **********.724486, "relative_start": 0.68084716796875, "end": **********.725136, "relative_end": **********.725136, "duration": 0.0006499290466308594, "duration_str": "650μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `byod` = ? and `assets`.`deleted_at` is null", "start": **********.726855, "relative_start": 0.6832160949707031, "end": **********.727395, "relative_end": **********.727395, "duration": 0.0005400180816650391, "duration_str": "540μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`next_audit_date` is not null and `assets`...", "start": **********.7294989, "relative_start": 0.6858599185943604, "end": **********.730089, "relative_end": **********.730089, "duration": 0.0005900859832763672, "duration_str": "590μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`next_audit_date` is not null and `assets`...", "start": **********.731966, "relative_start": 0.6883270740509033, "end": **********.732496, "relative_end": **********.732496, "duration": 0.0005300045013427734, "duration_str": "530μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`expected_checkin` is not null and `assets...", "start": **********.7344759, "relative_start": 0.6908369064331055, "end": **********.735086, "relative_end": **********.735086, "duration": 0.0006101131439208984, "duration_str": "610μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`expected_checkin` is not null and `assets...", "start": **********.737721, "relative_start": 0.6940820217132568, "end": **********.738671, "relative_end": **********.738671, "duration": 0.0009500980377197266, "duration_str": "950μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`deleted_at` is null", "start": **********.744236, "relative_start": 0.7005970478057861, "end": **********.744756, "relative_end": **********.744756, "duration": 0.0005199909210205078, "duration_str": "520μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `accessories` where `accessories`.`deleted_at` is null", "start": **********.747473, "relative_start": 0.703834056854248, "end": **********.747873, "relative_end": **********.747873, "duration": 0.0004000663757324219, "duration_str": "400μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `license_seats` where `deleted_at` is null and `license_seats`.`de...", "start": **********.750742, "relative_start": 0.7071030139923096, "end": **********.751262, "relative_end": **********.751262, "duration": 0.0005199909210205078, "duration_str": "520μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `consumables` where `consumables`.`deleted_at` is null", "start": **********.75358, "relative_start": 0.7099411487579346, "end": **********.75407, "relative_end": **********.75407, "duration": 0.0004899501800537109, "duration_str": "490μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `components` where `components`.`deleted_at` is null", "start": **********.756296, "relative_start": 0.7126569747924805, "end": **********.757006, "relative_end": **********.757006, "duration": 0.0007100105285644531, "duration_str": "710μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `users` where `users`.`deleted_at` is null", "start": **********.7587001, "relative_start": 0.7150611877441406, "end": **********.75921, "relative_end": **********.75921, "duration": 0.0005099773406982422, "duration_str": "510μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "Preparing Response", "start": **********.762133, "relative_start": 0.7184939384460449, "end": **********.894624, "relative_end": **********.894624, "duration": 0.1324911117553711, "duration_str": "132ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets...", "start": **********.770441, "relative_start": 0.7268021106719971, "end": **********.771861, "relative_end": **********.771861, "duration": 0.0014200210571289062, "duration_str": "1.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`expected_checkin` is not null and `assets...", "start": **********.7828, "relative_start": 0.7391610145568848, "end": **********.78402, "relative_end": **********.78402, "duration": 0.0012199878692626953, "duration_str": "1.22ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `next_audit_date` < ? and `next_audit_date` is not...", "start": **********.786737, "relative_start": 0.7430980205535889, "end": **********.787457, "relative_end": **********.787457, "duration": 0.0007200241088867188, "duration_str": "720μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `asset_eol_date` is not null and `asset_eol_date` <...", "start": **********.7901092, "relative_start": 0.7464702129364014, "end": **********.790729, "relative_end": **********.790729, "duration": 0.0006198883056640625, "duration_str": "620μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `warranty_months` is not null and DATE_ADD(purchase...", "start": **********.7931461, "relative_start": 0.7495071887969971, "end": **********.793786, "relative_end": **********.793786, "duration": 0.0006399154663085938, "duration_str": "640μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `consumables` where `qty` <= `min_amt` and `min_amt` > ? and `cons...", "start": **********.7965631, "relative_start": 0.7529242038726807, "end": **********.797063, "relative_end": **********.797063, "duration": 0.0004999637603759766, "duration_str": "500μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select `consumables`.*, (select count(*) from `consumables_users` where `consumables`.`id` = `consum...", "start": **********.836035, "relative_start": 0.792396068572998, "end": **********.837505, "relative_end": **********.837505, "duration": 0.0014700889587402344, "duration_str": "1.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select `accessories`.*, (select count(*) from `accessories_checkout` where `accessories`.`id` = `acc...", "start": **********.841324, "relative_start": 0.7976851463317871, "end": **********.841994, "relative_end": **********.841994, "duration": 0.0006699562072753906, "duration_str": "670μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `components` where `min_amt` is not null and `components`.`deleted_at` is null", "start": **********.8435779, "relative_start": 0.7999389171600342, "end": **********.843998, "relative_end": **********.843998, "duration": 0.0004200935363769531, "duration_str": "420μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `models` where `min_amt` > ? and `models`.`deleted_at` is null", "start": **********.8466802, "relative_start": 0.8030412197113037, "end": **********.84717, "relative_end": **********.84717, "duration": 0.0004899501800537109, "duration_str": "490μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `licenses` where `min_amt` > ? and `licenses`.`deleted_at` is null", "start": **********.8488288, "relative_start": 0.805189847946167, "end": **********.849259, "relative_end": **********.849259, "duration": 0.00043010711669921875, "duration_str": "430μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select `status_labels`.*, (select count(*) from `assets` where `status_labels`.`id` = `assets`.`stat...", "start": **********.871009, "relative_start": 0.8273701667785645, "end": **********.872029, "relative_end": **********.872029, "duration": 0.0010199546813964844, "duration_str": "1.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.2.12", "Environment": "production", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "UTC", "Locale": "en-US"}}, "event": {"count": 320, "start": **********.043639, "end": **********.898106, "duration": 0.****************, "duration_str": "854ms", "measures": [{"label": "Illuminate\\Routing\\Events\\Routing", "start": **********.675099, "relative_start": 0.****************, "end": **********.675099, "relative_end": **********.675099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\Routing"}, {"label": "Illuminate\\Routing\\Events\\RouteMatched", "start": **********.691088, "relative_start": 0.****************, "end": **********.691088, "relative_end": **********.691088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\RouteMatched"}, {"label": "Illuminate\\Foundation\\Events\\LocaleUpdated", "start": **********.701248, "relative_start": 0.****************, "end": **********.701248, "relative_end": **********.701248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Foundation\\Events\\LocaleUpdated"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.702608, "relative_start": 0.****************, "end": **********.702608, "relative_end": **********.702608, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.707825, "relative_start": 0.****************, "end": **********.707825, "relative_end": **********.707825, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "eloquent.booting: App\\Models\\Statuslabel", "start": **********.708779, "relative_start": 0.6651401519775391, "end": **********.708779, "relative_end": **********.708779, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booting"}, {"label": "eloquent.booted: App\\Models\\Statuslabel", "start": **********.708923, "relative_start": 0.6652841567993164, "end": **********.708923, "relative_end": **********.708923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.711016, "relative_start": 0.6673769950866699, "end": **********.711016, "relative_end": **********.711016, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.713395, "relative_start": 0.6697561740875244, "end": **********.713395, "relative_end": **********.713395, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.714137, "relative_start": 0.6704981327056885, "end": **********.714137, "relative_end": **********.714137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.716141, "relative_start": 0.6725020408630371, "end": **********.716141, "relative_end": **********.716141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.716617, "relative_start": 0.6729781627655029, "end": **********.716617, "relative_end": **********.716617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.718481, "relative_start": 0.674842119216919, "end": **********.718481, "relative_end": **********.718481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.719105, "relative_start": 0.6754660606384277, "end": **********.719105, "relative_end": **********.719105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.721469, "relative_start": 0.6778299808502197, "end": **********.721469, "relative_end": **********.721469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.722168, "relative_start": 0.6785290241241455, "end": **********.722168, "relative_end": **********.722168, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.724079, "relative_start": 0.6804399490356445, "end": **********.724079, "relative_end": **********.724079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.724657, "relative_start": 0.6810181140899658, "end": **********.724657, "relative_end": **********.724657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.72661, "relative_start": 0.6829710006713867, "end": **********.72661, "relative_end": **********.72661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.727014, "relative_start": 0.6833751201629639, "end": **********.727014, "relative_end": **********.727014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.728907, "relative_start": 0.6852681636810303, "end": **********.728907, "relative_end": **********.728907, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.729687, "relative_start": 0.6860480308532715, "end": **********.729687, "relative_end": **********.729687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.731541, "relative_start": 0.6879019737243652, "end": **********.731541, "relative_end": **********.731541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.732169, "relative_start": 0.6885299682617188, "end": **********.732169, "relative_end": **********.732169, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.733931, "relative_start": 0.6902921199798584, "end": **********.733931, "relative_end": **********.733931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.734644, "relative_start": 0.6910049915313721, "end": **********.734644, "relative_end": **********.734644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.737112, "relative_start": 0.6934731006622314, "end": **********.737112, "relative_end": **********.737112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.738065, "relative_start": 0.6944260597229004, "end": **********.738065, "relative_end": **********.738065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.740208, "relative_start": 0.6965689659118652, "end": **********.740208, "relative_end": **********.740208, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.744421, "relative_start": 0.700782060623169, "end": **********.744421, "relative_end": **********.744421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.747236, "relative_start": 0.7035970687866211, "end": **********.747236, "relative_end": **********.747236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.747584, "relative_start": 0.7039451599121094, "end": **********.747584, "relative_end": **********.747584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.749363, "relative_start": 0.7057240009307861, "end": **********.749363, "relative_end": **********.749363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "eloquent.booting: App\\Models\\LicenseSeat", "start": **********.75018, "relative_start": 0.7065410614013672, "end": **********.75018, "relative_end": **********.75018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booting"}, {"label": "eloquent.booted: App\\Models\\LicenseSeat", "start": **********.750538, "relative_start": 0.7068991661071777, "end": **********.750538, "relative_end": **********.750538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.750863, "relative_start": 0.7072241306304932, "end": **********.750863, "relative_end": **********.750863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.753272, "relative_start": 0.7096331119537354, "end": **********.753272, "relative_end": **********.753272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.753765, "relative_start": 0.7101261615753174, "end": **********.753765, "relative_end": **********.753765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.755918, "relative_start": 0.7122790813446045, "end": **********.755918, "relative_end": **********.755918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.756622, "relative_start": 0.7129831314086914, "end": **********.756622, "relative_end": **********.756622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.758443, "relative_start": 0.7148041725158691, "end": **********.758443, "relative_end": **********.758443, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.758883, "relative_start": 0.7152440547943115, "end": **********.758883, "relative_end": **********.758883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.760518, "relative_start": 0.71687912940979, "end": **********.760518, "relative_end": **********.760518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "creating: dashboard", "start": **********.761931, "relative_start": 0.****************, "end": **********.761931, "relative_end": **********.761931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "Illuminate\\Routing\\Events\\PreparingResponse", "start": **********.762135, "relative_start": 0.****************, "end": **********.762135, "relative_end": **********.762135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\PreparingResponse"}, {"label": "composing: dashboard", "start": **********.764964, "relative_start": 0.***************, "end": **********.764964, "relative_end": **********.764964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.770859, "relative_start": 0.****************, "end": **********.770859, "relative_end": **********.770859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.774699, "relative_start": 0.****************, "end": **********.774699, "relative_end": **********.774699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.776841, "relative_start": 0.7332019805908203, "end": **********.776841, "relative_end": **********.776841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.776877, "relative_start": 0.7332379817962646, "end": **********.776877, "relative_end": **********.776877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.77798, "relative_start": 0.7343411445617676, "end": **********.77798, "relative_end": **********.77798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.777997, "relative_start": 0.7343580722808838, "end": **********.777997, "relative_end": **********.777997, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.778453, "relative_start": 0.7348141670227051, "end": **********.778453, "relative_end": **********.778453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.778465, "relative_start": 0.7348260879516602, "end": **********.778465, "relative_end": **********.778465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.778807, "relative_start": 0.7351679801940918, "end": **********.778807, "relative_end": **********.778807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.778819, "relative_start": 0.735180139541626, "end": **********.778819, "relative_end": **********.778819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.779193, "relative_start": 0.7355539798736572, "end": **********.779193, "relative_end": **********.779193, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.779205, "relative_start": 0.7355661392211914, "end": **********.779205, "relative_end": **********.779205, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.779544, "relative_start": 0.7359051704406738, "end": **********.779544, "relative_end": **********.779544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.779556, "relative_start": 0.7359170913696289, "end": **********.779556, "relative_end": **********.779556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.779956, "relative_start": 0.7363171577453613, "end": **********.779956, "relative_end": **********.779956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.779968, "relative_start": 0.7363290786743164, "end": **********.779968, "relative_end": **********.779968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.780312, "relative_start": 0.73667311668396, "end": **********.780312, "relative_end": **********.780312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.780324, "relative_start": 0.736685037612915, "end": **********.780324, "relative_end": **********.780324, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.780687, "relative_start": 0.7370481491088867, "end": **********.780687, "relative_end": **********.780687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.780699, "relative_start": 0.7370600700378418, "end": **********.780699, "relative_end": **********.780699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.781038, "relative_start": 0.7373991012573242, "end": **********.781038, "relative_end": **********.781038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.781049, "relative_start": 0.7374100685119629, "end": **********.781049, "relative_end": **********.781049, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.781554, "relative_start": 0.7379150390625, "end": **********.781554, "relative_end": **********.781554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.781566, "relative_start": 0.7379269599914551, "end": **********.781566, "relative_end": **********.781566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.781895, "relative_start": 0.7382559776306152, "end": **********.781895, "relative_end": **********.781895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.781907, "relative_start": 0.7382681369781494, "end": **********.781907, "relative_end": **********.781907, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.783342, "relative_start": 0.7397029399871826, "end": **********.783342, "relative_end": **********.783342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.785548, "relative_start": 0.7419090270996094, "end": **********.785548, "relative_end": **********.785548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.78563, "relative_start": 0.7419910430908203, "end": **********.78563, "relative_end": **********.78563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.785641, "relative_start": 0.742002010345459, "end": **********.785641, "relative_end": **********.785641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.786035, "relative_start": 0.7423961162567139, "end": **********.786035, "relative_end": **********.786035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.786047, "relative_start": 0.742408037185669, "end": **********.786047, "relative_end": **********.786047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.787026, "relative_start": 0.74338698387146, "end": **********.787026, "relative_end": **********.787026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.788985, "relative_start": 0.7453460693359375, "end": **********.788985, "relative_end": **********.788985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.789088, "relative_start": 0.7454490661621094, "end": **********.789088, "relative_end": **********.789088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.789101, "relative_start": 0.7454619407653809, "end": **********.789101, "relative_end": **********.789101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.789503, "relative_start": 0.7458641529083252, "end": **********.789503, "relative_end": **********.789503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.789514, "relative_start": 0.7458751201629639, "end": **********.789514, "relative_end": **********.789514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.790353, "relative_start": 0.7467141151428223, "end": **********.790353, "relative_end": **********.790353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.79211, "relative_start": 0.7484710216522217, "end": **********.79211, "relative_end": **********.79211, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.792213, "relative_start": 0.7485740184783936, "end": **********.792213, "relative_end": **********.792213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.792225, "relative_start": 0.7485859394073486, "end": **********.792225, "relative_end": **********.792225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.7926, "relative_start": 0.7489609718322754, "end": **********.7926, "relative_end": **********.7926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.792612, "relative_start": 0.7489731311798096, "end": **********.792612, "relative_end": **********.792612, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.793321, "relative_start": 0.7496819496154785, "end": **********.793321, "relative_end": **********.793321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.795188, "relative_start": 0.7515490055084229, "end": **********.795188, "relative_end": **********.795188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.795271, "relative_start": 0.7516319751739502, "end": **********.795271, "relative_end": **********.795271, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.795283, "relative_start": 0.7516441345214844, "end": **********.795283, "relative_end": **********.795283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.795663, "relative_start": 0.7520241737365723, "end": **********.795663, "relative_end": **********.795663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.795675, "relative_start": 0.7520360946655273, "end": **********.795675, "relative_end": **********.795675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.796635, "relative_start": 0.7529959678649902, "end": **********.796635, "relative_end": **********.796635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.798505, "relative_start": 0.7548661231994629, "end": **********.798505, "relative_end": **********.798505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.798592, "relative_start": 0.754953145980835, "end": **********.798592, "relative_end": **********.798592, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.798604, "relative_start": 0.75496506690979, "end": **********.798604, "relative_end": **********.798604, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.799076, "relative_start": 0.7554371356964111, "end": **********.799076, "relative_end": **********.799076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.799088, "relative_start": 0.7554490566253662, "end": **********.799088, "relative_end": **********.799088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.80238, "relative_start": 0.7587411403656006, "end": **********.80238, "relative_end": **********.80238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.803114, "relative_start": 0.7594749927520752, "end": **********.803114, "relative_end": **********.803114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.803657, "relative_start": 0.7600181102752686, "end": **********.803657, "relative_end": **********.803657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.803906, "relative_start": 0.7602670192718506, "end": **********.803906, "relative_end": **********.803906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.804096, "relative_start": 0.7604570388793945, "end": **********.804096, "relative_end": **********.804096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.804269, "relative_start": 0.7606301307678223, "end": **********.804269, "relative_end": **********.804269, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: partials.bootstrap-table", "start": **********.804663, "relative_start": 0.761023998260498, "end": **********.804663, "relative_end": **********.804663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: partials.bootstrap-table", "start": **********.80469, "relative_start": 0.7610509395599365, "end": **********.80469, "relative_end": **********.80469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.807736, "relative_start": 0.7640969753265381, "end": **********.807736, "relative_end": **********.807736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.807753, "relative_start": 0.7641141414642334, "end": **********.807753, "relative_end": **********.807753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.80889, "relative_start": 0.7652511596679688, "end": **********.80889, "relative_end": **********.80889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.808905, "relative_start": 0.765265941619873, "end": **********.808905, "relative_end": **********.808905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.809285, "relative_start": 0.7656459808349609, "end": **********.809285, "relative_end": **********.809285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.809297, "relative_start": 0.7656581401824951, "end": **********.809297, "relative_end": **********.809297, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.809645, "relative_start": 0.7660059928894043, "end": **********.809645, "relative_end": **********.809645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.809656, "relative_start": 0.766016960144043, "end": **********.809656, "relative_end": **********.809656, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.809971, "relative_start": 0.7663321495056152, "end": **********.809971, "relative_end": **********.809971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.809982, "relative_start": 0.7663431167602539, "end": **********.809982, "relative_end": **********.809982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.810324, "relative_start": 0.7666850090026855, "end": **********.810324, "relative_end": **********.810324, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.810372, "relative_start": 0.7667331695556641, "end": **********.810372, "relative_end": **********.810372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.810831, "relative_start": 0.7671921253204346, "end": **********.810831, "relative_end": **********.810831, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.810844, "relative_start": 0.767204999923706, "end": **********.810844, "relative_end": **********.810844, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.81118, "relative_start": 0.7675411701202393, "end": **********.81118, "relative_end": **********.81118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.811191, "relative_start": 0.7675521373748779, "end": **********.811191, "relative_end": **********.811191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.811547, "relative_start": 0.7679080963134766, "end": **********.811547, "relative_end": **********.811547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.811558, "relative_start": 0.7679190635681152, "end": **********.811558, "relative_end": **********.811558, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.812746, "relative_start": 0.7691071033477783, "end": **********.812746, "relative_end": **********.812746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.81276, "relative_start": 0.7691211700439453, "end": **********.81276, "relative_end": **********.81276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.81316, "relative_start": 0.7695209980010986, "end": **********.81316, "relative_end": **********.81316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.813172, "relative_start": 0.7695331573486328, "end": **********.813172, "relative_end": **********.813172, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.813483, "relative_start": 0.7698440551757812, "end": **********.813483, "relative_end": **********.813483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.813494, "relative_start": 0.7698550224304199, "end": **********.813494, "relative_end": **********.813494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.814132, "relative_start": 0.7704930305480957, "end": **********.814132, "relative_end": **********.814132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.814145, "relative_start": 0.7705061435699463, "end": **********.814145, "relative_end": **********.814145, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.814494, "relative_start": 0.7708549499511719, "end": **********.814494, "relative_end": **********.814494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.814505, "relative_start": 0.7708661556243896, "end": **********.814505, "relative_end": **********.814505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.814814, "relative_start": 0.7711751461029053, "end": **********.814814, "relative_end": **********.814814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.814825, "relative_start": 0.771186113357544, "end": **********.814825, "relative_end": **********.814825, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.815162, "relative_start": 0.7715229988098145, "end": **********.815162, "relative_end": **********.815162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.815173, "relative_start": 0.7715339660644531, "end": **********.815173, "relative_end": **********.815173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.815506, "relative_start": 0.771867036819458, "end": **********.815506, "relative_end": **********.815506, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.815517, "relative_start": 0.7718780040740967, "end": **********.815517, "relative_end": **********.815517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.815822, "relative_start": 0.7721829414367676, "end": **********.815822, "relative_end": **********.815822, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.815834, "relative_start": 0.7721951007843018, "end": **********.815834, "relative_end": **********.815834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.816167, "relative_start": 0.7725281715393066, "end": **********.816167, "relative_end": **********.816167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.816177, "relative_start": 0.7725379467010498, "end": **********.816177, "relative_end": **********.816177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.816621, "relative_start": 0.772982120513916, "end": **********.816621, "relative_end": **********.816621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.816632, "relative_start": 0.7729930877685547, "end": **********.816632, "relative_end": **********.816632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.816958, "relative_start": 0.7733190059661865, "end": **********.816958, "relative_end": **********.816958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.816969, "relative_start": 0.7733299732208252, "end": **********.816969, "relative_end": **********.816969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: layouts.default", "start": **********.817778, "relative_start": 0.7741391658782959, "end": **********.817778, "relative_end": **********.817778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: layouts.default", "start": **********.817803, "relative_start": 0.7741639614105225, "end": **********.817803, "relative_end": **********.817803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.824247, "relative_start": 0.7806079387664795, "end": **********.824247, "relative_end": **********.824247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.824805, "relative_start": 0.7811660766601562, "end": **********.824805, "relative_end": **********.824805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.824823, "relative_start": 0.7811839580535889, "end": **********.824823, "relative_end": **********.824823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.825558, "relative_start": 0.781919002532959, "end": **********.825558, "relative_end": **********.825558, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.825744, "relative_start": 0.7821049690246582, "end": **********.825744, "relative_end": **********.825744, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.825757, "relative_start": 0.7821180820465088, "end": **********.825757, "relative_end": **********.825757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.826417, "relative_start": 0.7827780246734619, "end": **********.826417, "relative_end": **********.826417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.826587, "relative_start": 0.7829480171203613, "end": **********.826587, "relative_end": **********.826587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.826599, "relative_start": 0.7829599380493164, "end": **********.826599, "relative_end": **********.826599, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.82725, "relative_start": 0.7836110591888428, "end": **********.82725, "relative_end": **********.82725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.827364, "relative_start": 0.7837250232696533, "end": **********.827364, "relative_end": **********.827364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.827375, "relative_start": 0.783735990524292, "end": **********.827375, "relative_end": **********.827375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.827887, "relative_start": 0.7842481136322021, "end": **********.827887, "relative_end": **********.827887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.828015, "relative_start": 0.7843761444091797, "end": **********.828015, "relative_end": **********.828015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.828025, "relative_start": 0.784386157989502, "end": **********.828025, "relative_end": **********.828025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.828579, "relative_start": 0.7849400043487549, "end": **********.828579, "relative_end": **********.828579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.828676, "relative_start": 0.7850370407104492, "end": **********.828676, "relative_end": **********.828676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.828687, "relative_start": 0.7850480079650879, "end": **********.828687, "relative_end": **********.828687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.829182, "relative_start": 0.7855429649353027, "end": **********.829182, "relative_end": **********.829182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.829349, "relative_start": 0.7857100963592529, "end": **********.829349, "relative_end": **********.829349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.82945, "relative_start": 0.7858109474182129, "end": **********.82945, "relative_end": **********.82945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.829461, "relative_start": 0.7858221530914307, "end": **********.829461, "relative_end": **********.829461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.830002, "relative_start": 0.7863631248474121, "end": **********.830002, "relative_end": **********.830002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.830101, "relative_start": 0.7864620685577393, "end": **********.830101, "relative_end": **********.830101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.830113, "relative_start": 0.7864739894866943, "end": **********.830113, "relative_end": **********.830113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.830627, "relative_start": 0.7869880199432373, "end": **********.830627, "relative_end": **********.830627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.830733, "relative_start": 0.7870941162109375, "end": **********.830733, "relative_end": **********.830733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.830745, "relative_start": 0.7871060371398926, "end": **********.830745, "relative_end": **********.830745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.83126, "relative_start": 0.787621021270752, "end": **********.83126, "relative_end": **********.83126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.831374, "relative_start": 0.7877349853515625, "end": **********.831374, "relative_end": **********.831374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.831386, "relative_start": 0.7877471446990967, "end": **********.831386, "relative_end": **********.831386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.832008, "relative_start": 0.7883689403533936, "end": **********.832008, "relative_end": **********.832008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.832124, "relative_start": 0.788485050201416, "end": **********.832124, "relative_end": **********.832124, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.832136, "relative_start": 0.7884969711303711, "end": **********.832136, "relative_end": **********.832136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.832686, "relative_start": 0.7890470027923584, "end": **********.832686, "relative_end": **********.832686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.832792, "relative_start": 0.7891530990600586, "end": **********.832792, "relative_end": **********.832792, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.832803, "relative_start": 0.7891640663146973, "end": **********.832803, "relative_end": **********.832803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.833337, "relative_start": 0.7896981239318848, "end": **********.833337, "relative_end": **********.833337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "eloquent.booting: App\\Models\\ConsumableAssignment", "start": **********.833877, "relative_start": 0.7902381420135498, "end": **********.833877, "relative_end": **********.833877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booting"}, {"label": "eloquent.booted: App\\Models\\ConsumableAssignment", "start": **********.833996, "relative_start": 0.7903571128845215, "end": **********.833996, "relative_end": **********.833996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.836901, "relative_start": 0.7932620048522949, "end": **********.836901, "relative_end": **********.836901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.839903, "relative_start": 0.7962641716003418, "end": **********.839903, "relative_end": **********.839903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "eloquent.booting: App\\Models\\AccessoryCheckout", "start": **********.840883, "relative_start": 0.7972440719604492, "end": **********.840883, "relative_end": **********.840883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booting"}, {"label": "eloquent.booted: App\\Models\\AccessoryCheckout", "start": **********.840979, "relative_start": 0.7973401546478271, "end": **********.840979, "relative_end": **********.840979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.841641, "relative_start": 0.7980020046234131, "end": **********.841641, "relative_end": **********.841641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.843323, "relative_start": 0.7996840476989746, "end": **********.843323, "relative_end": **********.843323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.843731, "relative_start": 0.8000919818878174, "end": **********.843731, "relative_end": **********.843731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.845384, "relative_start": 0.8017449378967285, "end": **********.845384, "relative_end": **********.845384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "eloquent.booting: App\\Models\\AssetModel", "start": **********.846372, "relative_start": 0.8027329444885254, "end": **********.846372, "relative_end": **********.846372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booting"}, {"label": "eloquent.booted: App\\Models\\AssetModel", "start": **********.846505, "relative_start": 0.8028659820556641, "end": **********.846505, "relative_end": **********.846505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.846864, "relative_start": 0.803225040435791, "end": **********.846864, "relative_end": **********.846864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.848567, "relative_start": 0.8049280643463135, "end": **********.848567, "relative_end": **********.848567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.848994, "relative_start": 0.8053550720214844, "end": **********.848994, "relative_end": **********.848994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.850636, "relative_start": 0.8069970607757568, "end": **********.850636, "relative_end": **********.850636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Log\\Events\\MessageLogged", "start": **********.85112, "relative_start": 0.807481050491333, "end": **********.85112, "relative_end": **********.85112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Log\\Events\\MessageLogged"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.851285, "relative_start": 0.8076460361480713, "end": **********.851285, "relative_end": **********.851285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.851301, "relative_start": 0.8076620101928711, "end": **********.851301, "relative_end": **********.851301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.852058, "relative_start": 0.8084189891815186, "end": **********.852058, "relative_end": **********.852058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.863256, "relative_start": 0.8196170330047607, "end": **********.863256, "relative_end": **********.863256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.863283, "relative_start": 0.8196439743041992, "end": **********.863283, "relative_end": **********.863283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.864073, "relative_start": 0.8204340934753418, "end": **********.864073, "relative_end": **********.864073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.864223, "relative_start": 0.8205840587615967, "end": **********.864223, "relative_end": **********.864223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.864237, "relative_start": 0.8205981254577637, "end": **********.864237, "relative_end": **********.864237, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.864739, "relative_start": 0.8210999965667725, "end": **********.864739, "relative_end": **********.864739, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.864753, "relative_start": 0.8211140632629395, "end": **********.864753, "relative_end": **********.864753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.865362, "relative_start": 0.8217229843139648, "end": **********.865362, "relative_end": **********.865362, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.865445, "relative_start": 0.8218059539794922, "end": **********.865445, "relative_end": **********.865445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.865456, "relative_start": 0.82181715965271, "end": **********.865456, "relative_end": **********.865456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.865903, "relative_start": 0.8222639560699463, "end": **********.865903, "relative_end": **********.865903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.865916, "relative_start": 0.8222770690917969, "end": **********.865916, "relative_end": **********.865916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.866453, "relative_start": 0.8228139877319336, "end": **********.866453, "relative_end": **********.866453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.866526, "relative_start": 0.8228869438171387, "end": **********.866526, "relative_end": **********.866526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.866537, "relative_start": 0.8228981494903564, "end": **********.866537, "relative_end": **********.866537, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.866925, "relative_start": 0.8232860565185547, "end": **********.866925, "relative_end": **********.866925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.866937, "relative_start": 0.8232979774475098, "end": **********.866937, "relative_end": **********.866937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.867518, "relative_start": 0.8238790035247803, "end": **********.867518, "relative_end": **********.867518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.867584, "relative_start": 0.8239450454711914, "end": **********.867584, "relative_end": **********.867584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.867595, "relative_start": 0.8239560127258301, "end": **********.867595, "relative_end": **********.867595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.867973, "relative_start": 0.8243341445922852, "end": **********.867973, "relative_end": **********.867973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.867985, "relative_start": 0.8243460655212402, "end": **********.867985, "relative_end": **********.867985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.868489, "relative_start": 0.8248500823974609, "end": **********.868489, "relative_end": **********.868489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.868565, "relative_start": 0.8249261379241943, "end": **********.868565, "relative_end": **********.868565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.868575, "relative_start": 0.8249361515045166, "end": **********.868575, "relative_end": **********.868575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.869103, "relative_start": 0.8254640102386475, "end": **********.869103, "relative_end": **********.869103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.869199, "relative_start": 0.8255600929260254, "end": **********.869199, "relative_end": **********.869199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.869209, "relative_start": 0.8255701065063477, "end": **********.869209, "relative_end": **********.869209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.869631, "relative_start": 0.8259921073913574, "end": **********.869631, "relative_end": **********.869631, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.869683, "relative_start": 0.8260440826416016, "end": **********.869683, "relative_end": **********.869683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.870213, "relative_start": 0.8265740871429443, "end": **********.870213, "relative_end": **********.870213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.870227, "relative_start": 0.8265881538391113, "end": **********.870227, "relative_end": **********.870227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.871423, "relative_start": 0.8277840614318848, "end": **********.871423, "relative_end": **********.871423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.873543, "relative_start": 0.8299040794372559, "end": **********.873543, "relative_end": **********.873543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.873731, "relative_start": 0.8300919532775879, "end": **********.873731, "relative_end": **********.873731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.873745, "relative_start": 0.8301060199737549, "end": **********.873745, "relative_end": **********.873745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.874194, "relative_start": 0.8305549621582031, "end": **********.874194, "relative_end": **********.874194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.874208, "relative_start": 0.8305690288543701, "end": **********.874208, "relative_end": **********.874208, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.874579, "relative_start": 0.8309400081634521, "end": **********.874579, "relative_end": **********.874579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.87459, "relative_start": 0.8309509754180908, "end": **********.87459, "relative_end": **********.87459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.874955, "relative_start": 0.8313159942626953, "end": **********.874955, "relative_end": **********.874955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.874968, "relative_start": 0.8313291072845459, "end": **********.874968, "relative_end": **********.874968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.875319, "relative_start": 0.8316800594329834, "end": **********.875319, "relative_end": **********.875319, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.875331, "relative_start": 0.8316919803619385, "end": **********.875331, "relative_end": **********.875331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.875703, "relative_start": 0.832064151763916, "end": **********.875703, "relative_end": **********.875703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.875716, "relative_start": 0.8320770263671875, "end": **********.875716, "relative_end": **********.875716, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.876058, "relative_start": 0.8324191570281982, "end": **********.876058, "relative_end": **********.876058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.876069, "relative_start": 0.8324301242828369, "end": **********.876069, "relative_end": **********.876069, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.876664, "relative_start": 0.8330249786376953, "end": **********.876664, "relative_end": **********.876664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.876794, "relative_start": 0.8331551551818848, "end": **********.876794, "relative_end": **********.876794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.876806, "relative_start": 0.8331670761108398, "end": **********.876806, "relative_end": **********.876806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.877371, "relative_start": 0.8337321281433105, "end": **********.877371, "relative_end": **********.877371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.877482, "relative_start": 0.8338429927825928, "end": **********.877482, "relative_end": **********.877482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.877493, "relative_start": 0.8338539600372314, "end": **********.877493, "relative_end": **********.877493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.878054, "relative_start": 0.8344149589538574, "end": **********.878054, "relative_end": **********.878054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.878322, "relative_start": 0.8346829414367676, "end": **********.878322, "relative_end": **********.878322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.878616, "relative_start": 0.8349771499633789, "end": **********.878616, "relative_end": **********.878616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.878881, "relative_start": 0.8352420330047607, "end": **********.878881, "relative_end": **********.878881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.879106, "relative_start": 0.8354671001434326, "end": **********.879106, "relative_end": **********.879106, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.87934, "relative_start": 0.8357009887695312, "end": **********.87934, "relative_end": **********.87934, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.879403, "relative_start": 0.8357641696929932, "end": **********.879403, "relative_end": **********.879403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.879414, "relative_start": 0.8357751369476318, "end": **********.879414, "relative_end": **********.879414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.879964, "relative_start": 0.8363251686096191, "end": **********.879964, "relative_end": **********.879964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.880032, "relative_start": 0.8363931179046631, "end": **********.880032, "relative_end": **********.880032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.880042, "relative_start": 0.8364031314849854, "end": **********.880042, "relative_end": **********.880042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.880562, "relative_start": 0.8369231224060059, "end": **********.880562, "relative_end": **********.880562, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.880622, "relative_start": 0.8369829654693604, "end": **********.880622, "relative_end": **********.880622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.880633, "relative_start": 0.8369941711425781, "end": **********.880633, "relative_end": **********.880633, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.881136, "relative_start": 0.8374969959259033, "end": **********.881136, "relative_end": **********.881136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.881206, "relative_start": 0.8375670909881592, "end": **********.881206, "relative_end": **********.881206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.881217, "relative_start": 0.8375780582427979, "end": **********.881217, "relative_end": **********.881217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.881697, "relative_start": 0.8380579948425293, "end": **********.881697, "relative_end": **********.881697, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.881921, "relative_start": 0.8382821083068848, "end": **********.881921, "relative_end": **********.881921, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.881934, "relative_start": 0.8382949829101562, "end": **********.881934, "relative_end": **********.881934, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.88243, "relative_start": 0.8387911319732666, "end": **********.88243, "relative_end": **********.88243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.882517, "relative_start": 0.8388781547546387, "end": **********.882517, "relative_end": **********.882517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.882527, "relative_start": 0.8388881683349609, "end": **********.882527, "relative_end": **********.882527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.883006, "relative_start": 0.839367151260376, "end": **********.883006, "relative_end": **********.883006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.883104, "relative_start": 0.8394651412963867, "end": **********.883104, "relative_end": **********.883104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.883114, "relative_start": 0.839475154876709, "end": **********.883114, "relative_end": **********.883114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.883609, "relative_start": 0.8399701118469238, "end": **********.883609, "relative_end": **********.883609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.883665, "relative_start": 0.8400261402130127, "end": **********.883665, "relative_end": **********.883665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.883675, "relative_start": 0.840036153793335, "end": **********.883675, "relative_end": **********.883675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.884022, "relative_start": 0.8403830528259277, "end": **********.884022, "relative_end": **********.884022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.884033, "relative_start": 0.8403940200805664, "end": **********.884033, "relative_end": **********.884033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.884495, "relative_start": 0.8408560752868652, "end": **********.884495, "relative_end": **********.884495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.885243, "relative_start": 0.8416039943695068, "end": **********.885243, "relative_end": **********.885243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.885482, "relative_start": 0.8418431282043457, "end": **********.885482, "relative_end": **********.885482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.885709, "relative_start": 0.8420701026916504, "end": **********.885709, "relative_end": **********.885709, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.885915, "relative_start": 0.8422760963439941, "end": **********.885915, "relative_end": **********.885915, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.886115, "relative_start": 0.8424761295318604, "end": **********.886115, "relative_end": **********.886115, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.886336, "relative_start": 0.8426971435546875, "end": **********.886336, "relative_end": **********.886336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.886529, "relative_start": 0.8428900241851807, "end": **********.886529, "relative_end": **********.886529, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.886714, "relative_start": 0.8430750370025635, "end": **********.886714, "relative_end": **********.886714, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.886901, "relative_start": 0.8432619571685791, "end": **********.886901, "relative_end": **********.886901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.887083, "relative_start": 0.8434441089630127, "end": **********.887083, "relative_end": **********.887083, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.887141, "relative_start": 0.8435020446777344, "end": **********.887141, "relative_end": **********.887141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.887152, "relative_start": 0.843513011932373, "end": **********.887152, "relative_end": **********.887152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.887574, "relative_start": 0.8439350128173828, "end": **********.887574, "relative_end": **********.887574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.887586, "relative_start": 0.843947172164917, "end": **********.887586, "relative_end": **********.887586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.888412, "relative_start": 0.8447730541229248, "end": **********.888412, "relative_end": **********.888412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.888493, "relative_start": 0.8448541164398193, "end": **********.888493, "relative_end": **********.888493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.888504, "relative_start": 0.844865083694458, "end": **********.888504, "relative_end": **********.888504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.889364, "relative_start": 0.8457250595092773, "end": **********.889364, "relative_end": **********.889364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.889376, "relative_start": 0.8457369804382324, "end": **********.889376, "relative_end": **********.889376, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: notifications", "start": **********.890626, "relative_start": 0.8469870090484619, "end": **********.890626, "relative_end": **********.890626, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: notifications", "start": **********.890654, "relative_start": 0.****************, "end": **********.890654, "relative_end": **********.890654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Routing\\Events\\ResponsePrepared", "start": **********.894638, "relative_start": 0.***************, "end": **********.894638, "relative_end": **********.894638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\ResponsePrepared"}], "nb_measures": 320}, "views": {"count": 95, "nb_templates": 95, "templates": [{"name": "1x dashboard", "param_count": null, "params": [], "start": **********.765155, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.phpdashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "dashboard"}, {"name": "91x 2548ffbf4d41f5cc86ea949d4376e84f::icon", "param_count": null, "params": [], "start": **********.777051, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Providers/../../resources/views/blade/icon.blade.php2548ffbf4d41f5cc86ea949d4376e84f::icon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fblade%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}, "render_count": 91, "name_original": "2548ffbf4d41f5cc86ea949d4376e84f::icon"}, {"name": "1x partials.bootstrap-table", "param_count": null, "params": [], "start": **********.804833, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/partials/bootstrap-table.blade.phppartials.bootstrap-table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fpartials%2Fbootstrap-table.blade.php&line=1", "ajax": false, "filename": "bootstrap-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.bootstrap-table"}, {"name": "1x layouts.default", "param_count": null, "params": [], "start": **********.817966, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/default.blade.phplayouts.default", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Flayouts%2Fdefault.blade.php&line=1", "ajax": false, "filename": "default.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.default"}, {"name": "1x notifications", "param_count": null, "params": [], "start": **********.890776, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/notifications.blade.phpnotifications", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fnotifications.blade.php&line=1", "ajax": false, "filename": "notifications.blade.php", "line": "?"}, "render_count": 1, "name_original": "notifications"}]}, "route": {"uri": "GET /", "middleware": "web, auth, breadcrumbs", "controller": "\\App\\Http\\Controllers\\DashboardController@index<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=27\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "home", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=27\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/DashboardController.php:27-53</a>"}, "queries": {"count": 30, "nb_statements": 30, "nb_visible_statements": 30, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02169, "accumulated_duration_str": "21.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `assets` where `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 37}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 21, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.70219, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:37", "source": {"index": 19, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=37", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "37"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `deployable` = 0 and `pending` = 0 and `archived` = 1 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0, 0, 1], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.7105298, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:39", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=39", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "39"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`assigned_to` is null and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `deployable` = 1 and `pending` = 0 and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.713867, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:47", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=47", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "47"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assigned_to` > '0' and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["0"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.716429, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:54", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=54", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "54"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `deployable` = 0 and `pending` = 0 and `archived` = 1 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0, 0, 1], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 61}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.718915, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:61", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=61", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "61"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `deployable` = 0 and `pending` = 1 and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0, 1, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 68}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.721918, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:68", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=68", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "68"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `deployable` = 0 and `pending` = 0 and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0, 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 75}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.724486, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:75", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=75", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "75"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `byod` = '1' and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 82}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.726855, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:82", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 82}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=82", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "82"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`next_audit_date` is not null and `assets`.`next_audit_date` between '2025-08-16' and '2025-08-16' and `assets`.`archived` = 0 and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16", "2025-08-16", 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 89}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.7294989, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:89", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=89", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "89"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`next_audit_date` is not null and `assets`.`next_audit_date` < '2025-08-16' and `assets`.`archived` = 0 and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16", 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 96}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.731966, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:96", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=96", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "96"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`expected_checkin` is not null and `assets`.`expected_checkin` between '2025-08-16' and '2025-08-16' and `assets`.`archived` = 0 and `assets`.`assigned_to` is not null and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16", "2025-08-16", 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 103}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.7344759, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:103", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=103", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "103"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`expected_checkin` is not null and `assets`.`expected_checkin` < '2025-08-16' and `assets`.`archived` = 0 and `assets`.`assigned_to` is not null and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16", 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 110}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.737721, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:110", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=110", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "110"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 33}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.744236, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:33", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=33", "ajax": false, "filename": "DashboardController.php", "line": "33"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `accessories` where `accessories`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 34}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.747473, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:34", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=34", "ajax": false, "filename": "DashboardController.php", "line": "34"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `license_seats` where `deleted_at` is null and `license_seats`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/License.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\License.php", "line": 452}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.750742, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "License.php:452", "source": {"index": 16, "namespace": null, "name": "app/Models/License.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\License.php", "line": 452}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FModels%2FLicense.php&line=452", "ajax": false, "filename": "License.php", "line": "452"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `consumables` where `consumables`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.75358, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:36", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=36", "ajax": false, "filename": "DashboardController.php", "line": "36"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `components` where `components`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 37}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.756296, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:37", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=37", "ajax": false, "filename": "DashboardController.php", "line": "37"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `users` where `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.7587001, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:38", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=38", "ajax": false, "filename": "DashboardController.php", "line": "38"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 39}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.770441, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "dashboard:39", "source": {"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fdashboard.blade.php&line=39", "ajax": false, "filename": "dashboard.blade.php", "line": "39"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`expected_checkin` is not null and `assets`.`expected_checkin` < '2025-08-16' and `assets`.`archived` = 0 and `assets`.`assigned_to` is not null and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16", 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 400}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.7828, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "dashboard:400", "source": {"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 400}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fdashboard.blade.php&line=400", "ajax": false, "filename": "dashboard.blade.php", "line": "400"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `next_audit_date` < '2025-08-16 08:41:22' and `next_audit_date` is not null and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16 08:41:22"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 457}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.786737, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "dashboard:457", "source": {"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 457}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fdashboard.blade.php&line=457", "ajax": false, "filename": "dashboard.blade.php", "line": "457"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `asset_eol_date` is not null and `asset_eol_date` <= '2025-08-16 08:41:22' and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16 08:41:22"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 513}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.7901092, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "dashboard:513", "source": {"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 513}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fdashboard.blade.php&line=513", "ajax": false, "filename": "dashboard.blade.php", "line": "513"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `warranty_months` is not null and DATE_ADD(purchase_date, INTERVAL warranty_months MONTH) < NOW() and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 569}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.7931461, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "dashboard:569", "source": {"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 569}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fdashboard.blade.php&line=569", "ajax": false, "filename": "dashboard.blade.php", "line": "569"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `consumables` where `qty` <= `min_amt` and `min_amt` > 0 and `consumables`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 625}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.7965631, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "dashboard:625", "source": {"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fdashboard.blade.php&line=625", "ajax": false, "filename": "dashboard.blade.php", "line": "625"}, "connection": "snipeit", "explain": null}, {"sql": "select `consumables`.*, (select count(*) from `consumables_users` where `consumables`.`id` = `consumables_users`.`consumable_id`) as `consumable_assignments_count` from `consumables` where `min_amt` is not null and `consumables`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 748}, {"index": 16, "namespace": "view", "name": "layouts.default", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/default.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.836035, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "Helper.php:748", "source": {"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 748}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHelpers%2FHelper.php&line=748", "ajax": false, "filename": "Helper.php", "line": "748"}, "connection": "snipeit", "explain": null}, {"sql": "select `accessories`.*, (select count(*) from `accessories_checkout` where `accessories`.`id` = `accessories_checkout`.`accessory_id`) as `checkouts_count` from `accessories` where `min_amt` is not null and `accessories`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 749}, {"index": 16, "namespace": "view", "name": "layouts.default", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/default.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.841324, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Helper.php:749", "source": {"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 749}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHelpers%2FHelper.php&line=749", "ajax": false, "filename": "Helper.php", "line": "749"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `components` where `min_amt` is not null and `components`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 750}, {"index": 16, "namespace": "view", "name": "layouts.default", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/default.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.8435779, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Helper.php:750", "source": {"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 750}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHelpers%2FHelper.php&line=750", "ajax": false, "filename": "Helper.php", "line": "750"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `models` where `min_amt` > 0 and `models`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 751}, {"index": 16, "namespace": "view", "name": "layouts.default", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/default.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.8466802, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Helper.php:751", "source": {"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 751}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHelpers%2FHelper.php&line=751", "ajax": false, "filename": "Helper.php", "line": "751"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `licenses` where `min_amt` > 0 and `licenses`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 752}, {"index": 16, "namespace": "view", "name": "layouts.default", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/default.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.8488288, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Helper.php:752", "source": {"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 752}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHelpers%2FHelper.php&line=752", "ajax": false, "filename": "Helper.php", "line": "752"}, "connection": "snipeit", "explain": null}, {"sql": "select `status_labels`.*, (select count(*) from `assets` where `status_labels`.`id` = `assets`.`status_id` and `assets`.`deleted_at` is null) as `asset_count` from `status_labels` where `show_in_nav` = 1 and `status_labels`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "layouts.default", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/default.blade.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.871009, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "layouts.default:1004", "source": {"index": 15, "namespace": "view", "name": "layouts.default", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/default.blade.php", "line": 1004}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Flayouts%2Fdefault.blade.php&line=1004", "ajax": false, "filename": "default.blade.php", "line": "1004"}, "connection": "snipeit", "explain": null}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "logs": {"count": 0, "messages": []}, "files": {"messages": [{"message": "'\\server.php',", "is_string": true}, {"message": "'\\public\\index.php',", "is_string": true}, {"message": "'\\vendor\\autoload.php',", "is_string": true}, {"message": "'\\vendor\\composer\\autoload_real.php',", "is_string": true}, {"message": "'\\vendor\\composer\\platform_check.php',", "is_string": true}, {"message": "'\\vendor\\composer\\ClassLoader.php',", "is_string": true}, {"message": "'\\vendor\\composer\\autoload_static.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\deprecation-contracts\\function.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-mbstring\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-mbstring\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-normalizer\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-normalizer\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-ctype\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-ctype\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-grapheme\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-grapheme\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\string\\Resources\\functions.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-php83\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-php83\\bootstrap81.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Resources\\functions\\dump.php',", "is_string": true}, {"message": "'\\vendor\\ralouphie\\getallheaders\\src\\getallheaders.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-php80\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-idn\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-idn\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\react\\promise\\src\\functions_include.php',", "is_string": true}, {"message": "'\\vendor\\react\\promise\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\functions_include.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\prompts\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\termwind\\src\\Functions.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\clock\\Resources\\now.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-uuid\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-uuid\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Resources\\functions.php',", "is_string": true}, {"message": "'\\vendor\\ramsey\\uuid\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\fakerphp\\faker\\src\\Faker\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\mtdowling\\jmespath.php\\src\\JmesPath.php',", "is_string": true}, {"message": "'\\vendor\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock\\autoload.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\php-text-template\\src\\Template.php',", "is_string": true}, {"message": "'\\vendor\\phpseclib\\phpseclib\\phpseclib\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\flare-client-php\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-php81\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\aws\\aws-sdk-php\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\ezyang\\htmlpurifier\\library\\HTMLPurifier.composer.php',", "is_string": true}, {"message": "'\\vendor\\phpstan\\phpstan\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php',", "is_string": true}, {"message": "'\\vendor\\psy\\psysh\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\helpers\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\league\\csv\\src\\functions_include.php',", "is_string": true}, {"message": "'\\vendor\\league\\csv\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\mockery\\mockery\\library\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\mockery\\mockery\\library\\Mockery.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\collision\\src\\Adapters\\Phpunit\\Autoload.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Runner\\Version.php',", "is_string": true}, {"message": "'\\vendor\\sebastian\\version\\src\\Version.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\collision\\src\\Adapters\\Phpunit\\Subscribers\\EnsurePrinterIsRegisteredSubscriber.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Event\\Events\\Application\\StartedSubscriber.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Event\\Subscriber.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\autoload.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\autoload-php7.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\src\\Compat.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\namespaced.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\sodium_compat.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\constants.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\php84compat_const.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\php84compat.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\stream-xchacha20.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\ristretto255.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock-phpunit\\autoload.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Rule\\InvocationOrder.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\SelfDescribing.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Invocation.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Interface\\MockObject.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Interface\\Stub.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\InvocationMocker.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\InvocationStubber.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\MethodNameMatch.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\ParametersMatch.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\Stub.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\Identity.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Rule\\MethodName.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\InvocationHandler.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock-phpunit\\classes\\MockDisablerPHPUnit10.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Event\\Events\\Test\\Lifecycle\\FinishedSubscriber.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock-phpunit\\classes\\DefaultArgumentRemoverReturnTypes100.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock-phpunit\\classes\\MockObjectProxyReturnTypes100.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-backup\\src\\Helpers\\functions.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\helpers.php',", "is_string": true}, {"message": "'\\bootstrap\\app.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Container\\Container.php',", "is_string": true}, {"message": "'\\vendor\\psr\\container\\src\\ContainerInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Foundation\\Application.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Foundation\\CachesConfiguration.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Foundation\\CachesRoutes.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-kernel\\HttpKernelInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\EventServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\Context\\ContextServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RoutingServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Http\\Kernel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\InteractsWithTime.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Http\\Kernel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Container\\ContextualAttribute.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Tappable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Routing\\BindingRegistrar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Routing\\Registrar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ReflectsClosures.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Events\\Dispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollectionInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Request.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\Request.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\AcceptHeader.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\AcceptHeaderItem.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\FileBag.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\ParameterBag.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\HeaderBag.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\HeaderUtils.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\InputBag.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\ServerBag.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Concerns\\CanBePrecognitive.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Concerns\\InteractsWithContentTypes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Concerns\\InteractsWithFlashData.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Concerns\\InteractsWithInput.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Dumpable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\InteractsWithData.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Conditionable\\Traits\\Conditionable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Arrayable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Carbon.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Carbon.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Date.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Boundaries.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Comparison.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Converter.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\ToStringFormat.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Creator.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\ObjectInitialisation.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\LocalFactory.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Difference.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Macro.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Mixin.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\MagicParameter.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Modifiers.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Mutability.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Cast.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Options.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\StaticOptions.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Localization.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\StaticLocalization.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Rounding.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\IntervalRounding.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Serialization.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Test.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Timestamp.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Units.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Week.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonTimeZone.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonInterval.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\IntervalStep.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonConverterInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\FactoryImmutable.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\clock\\ClockInterface.php',", "is_string": true}, {"message": "'\\vendor\\psr\\clock\\src\\ClockInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonImmutable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadEnvironmentVariables.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Env.php',", "is_string": true}, {"message": "'\\vendor\\phpoption\\phpoption\\src\\PhpOption\\Option.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\RepositoryBuilder.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\ServerConstAdapter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\AdapterInterface.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\ReaderInterface.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\WriterInterface.php',", "is_string": true}, {"message": "'\\vendor\\phpoption\\phpoption\\src\\PhpOption\\Some.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\EnvConstAdapter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\PutenvAdapter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\MultiReader.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\MultiWriter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\ImmutableWriter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\AdapterRepository.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\RepositoryInterface.php',", "is_string": true}, {"message": "'\\vendor\\phpoption\\phpoption\\src\\PhpOption\\None.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php',", "is_string": true}, {"message": "'\\bootstrap\\cache\\config.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Config\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Config\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\EnvironmentDetector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\AliasLoader.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\CanBeEscapedWhenCastToString.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Enumerable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Jsonable.php',", "is_string": true}, {"message": "'\\bootstrap\\cache\\packages.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php',", "is_string": true}, {"message": "'\\bootstrap\\cache\\services.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\CookieServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasEvents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasGlobalScopes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasRelationships.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasTimestamps.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasUniqueIds.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HidesAttributes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\PreventsCircularRecursion.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\HasCollection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Broadcasting\\HasBroadcastChannel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Queue\\QueueableEntity.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Routing\\UrlRoutable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\SerializableClosure.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Signers\\Hmac.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Contracts\\Signer.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Serializers\\Signed.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Contracts\\Serializable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FoundationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\AggregateServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Testing\\ParallelTestingServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Testing\\Concerns\\TestDatabases.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\DeferrableProvider.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\AbstractCloner.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\ClonerInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\HtmlDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Dumper\\HtmlDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Dumper\\CliDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Dumper\\AbstractDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Dumper\\DataDumperInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\DumperInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Concerns\\ResolvesDumpSource.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\VarCloner.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\HigherOrderTapProxy.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Caster\\ReflectionCaster.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\VarDumper.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Uri.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Htmlable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Responsable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Reflector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\PaginationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\PaginationState.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\Paginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Pagination\\Paginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\CursorPaginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractCursorPaginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Pagination\\CursorPaginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\arietimmerman\\laravel-scim-server\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-dompdf\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\eduardokum\\laravel-mail-auto-embed\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\intervention\\image\\src\\Intervention\\Image\\ImageServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\intervention\\image\\src\\Intervention\\Image\\ImageServiceProviderLaravelRecent.php',", "is_string": true}, {"message": "'\\vendor\\laravel-notification-channels\\google-chat\\src\\GoogleChatServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel-notification-channels\\microsoft-teams\\src\\MicrosoftTeamsServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Notification.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Manager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Notifications\\Dispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Notifications\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\Passport.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\ContextualBindingBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Container\\ContextualBindingBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Auth.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\slack-notification-channel\\src\\SlackChannelServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\ui\\src\\UiServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\LivewireServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\LivewireManager.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\EventBus.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\PersistentMiddleware\\PersistentMiddleware.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\Mechanism.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\FrontendAssets\\FrontendAssets.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendBlade.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\CompileLivewireTags\\CompileLivewireTags.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\RenderComponent.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\DataStore.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Laravel\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\collision\\src\\Adapters\\Laravel\\CollisionServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\phpinsights\\src\\Application\\Adapters\\Laravel\\InsightsServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\phpinsights\\src\\Application\\Injectors\\Repositories.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\termwind\\src\\Laravel\\TermwindServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\osa-eg\\laravel-teams-notification\\src\\LaravelTeamsNotificationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\pragmarx\\google2fa-laravel\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-backup\\src\\BackupServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessAssets.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessBladeComponents.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessCommands.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessConfigs.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessInertia.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessMigrations.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessRoutes.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessServiceProviders.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessTranslations.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessViewComposers.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessViews.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessViewSharedData.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Package.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasAssets.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasBladeComponents.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasCommands.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasConfigs.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasInertia.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasInstallCommand.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasMigrations.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasRoutes.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasServiceProviders.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasTranslations.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasViewComposers.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasViews.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasViewSharedData.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\IgnitionServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\ignition\\src\\Config\\IgnitionConfig.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\ignition\\src\\Config\\FileConfigManager.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\ignition\\src\\Contracts\\ConfigManager.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\error-solutions\\src\\SolutionProviderRepository.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\error-solutions\\src\\Contracts\\SolutionProviderRepository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Log.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\ParsesLogConfiguration.php',", "is_string": true}, {"message": "'\\vendor\\psr\\log\\src\\LoggerInterface.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Level.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-signal-aware-command\\src\\SignalAwareCommandServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\BreadcrumbsServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Route.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CreatesRegularExpressionRouteConstraints.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\FiltersControllerMiddleware.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php',", "is_string": true}, {"message": "'\\vendor\\unicodeveloper\\laravel-password\\src\\DumbPasswordServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\AppServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\AuthServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\AuthServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\EventServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\RouteServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\SettingsServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\ValidationServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\BladeServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\LivewireServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\MacroServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\SamlServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\BreadcrumbsServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\ConnectionResolverInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Renderer\\Listener.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteBinding.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\debugbar-routes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteAction.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteUri.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteGroup.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\console\\Application.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\service-contracts\\ResetInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Console\\Application.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Event.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\routes\\web.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\ui\\src\\AuthRouteMethods.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\AboutCommand.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\console\\Command\\Command.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\console\\Command\\SignalableCommandInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\ConfiguresPrompts.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\HasParameters.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\InteractsWithIO.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\InteractsWithSignals.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\PromptsForMissingInput.php',", "is_string": true}, {"message": "'\\vendor\\composer\\InstalledVersions.php',", "is_string": true}, {"message": "'\\vendor\\composer\\installed.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\console\\Output\\OutputInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Stringable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Blade.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Compiler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesAuthorizations.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesClasses.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesComments.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesComponents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesConditionals.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesEchos.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesErrors.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesFragments.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesHelpers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesIncludes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesInjections.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesJson.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesJs.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesLayouts.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesLoops.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesRawPhp.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesSessions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesStacks.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesStyles.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesTranslations.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesUseStatements.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\CompilerInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\EngineResolver.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\CompileLivewireTags\\LivewireTagPrecompiler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\ComponentHookRegistry.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportWireModelingNestedComponents\\SupportWireModelingNestedComponents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\ComponentHook.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportMultipleRootElementDetection\\SupportMultipleRootElementDetection.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\SupportDisablingBackButtonCache.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportNestedComponentListeners\\SupportNestedComponentListeners.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportMorphAwareIfStatement\\SupportMorphAwareIfStatement.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Livewire.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportAutoInjectedAssets\\SupportAutoInjectedAssets.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportComputed\\SupportLegacyComputedPropertySyntax.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportNestingComponents\\SupportNestingComponents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportScriptsAndAssets\\SupportScriptsAndAssets.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportBladeAttributes\\SupportBladeAttributes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ComponentAttributeBag.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportConsoleCommands\\SupportConsoleCommands.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\View\\View.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Renderable.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportReactiveProps\\SupportReactiveProps.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportFileDownloads\\SupportFileDownloads.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportJsEvaluation\\SupportJsEvaluation.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportQueryString\\SupportQueryString.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportFileUploads\\SupportFileUploads.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportTeleporting\\SupportTeleporting.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportLazyLoading\\SupportLazyLoading.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportFormObjects\\SupportFormObjects.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportAttributes\\SupportAttributes.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination\\SupportPagination.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportValidation\\SupportValidation.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportIsolating\\SupportIsolating.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportRedirects\\SupportRedirects.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportStreaming\\SupportStreaming.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportNavigate\\SupportNavigate.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Vite.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportEntangle\\SupportEntangle.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportLocales\\SupportLocales.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportTesting\\SupportTesting.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\SupportModels.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportEvents\\SupportEvents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportLegacyModels\\SupportLegacyModels.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportWireables\\SupportWireables.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Translator.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation-contracts\\TranslatorInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Translator.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\MessageCatalogue.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\MessageCatalogueInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\MetadataAwareInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\CatalogueMetadataAwareInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\TranslatorBagInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation-contracts\\LocaleAwareInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\lazy\\Carbon\\TranslatorStrongType.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\AbstractTranslator.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\TranslatorStrongTypeInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Loader\\ArrayLoader.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Loader\\LoaderInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\MessageFormatter\\MessageFormatterMapper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Formatter\\MessageFormatterInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\lazy\\Carbon\\MessageFormatter\\MessageFormatterMapperStrongType.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Formatter\\MessageFormatter.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Formatter\\IntlFormatter.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Formatter\\IntlFormatterInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\IdentityTranslator.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation-contracts\\TranslatorTrait.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Lang\\en.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Lang\\en_US.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonPeriod.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\lazy\\Carbon\\UnprotectedDatePeriod.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Date.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\DateFactory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Events\\LocaleUpdated.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-backup\\src\\Notifications\\EventHandler.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-backup\\src\\Listeners\\EncryptBackupArchive.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\ignition-routes.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Http\\Controllers\\HealthCheckController.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Http\\Controllers\\ExecuteSolutionController.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Validation\\ValidatesRequests.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Http\\Controllers\\UpdateConfigController.php',", "is_string": true}, {"message": "'\\app\\Exceptions\\Handler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Debug\\ExceptionHandler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\ReportableHandler.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\DumpRecorder\\DumpRecorder.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\DumpRecorder\\MultiDumpHandler.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\JobRecorder\\JobRecorder.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\LogRecorder\\LogRecorder.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\QueryRecorder\\QueryRecorder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\QueueServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Serializers\\Native.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\QueueManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Queue\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Queue\\Monitor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Validator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Validation\\Factory.php',", "is_string": true}, {"message": "'\\app\\Providers\\SnipeTranslationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Translation\\TranslationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Translation\\FileLoader.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Translation\\Loader.php',", "is_string": true}, {"message": "'\\app\\Services\\SnipeTranslator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Translation\\Translator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\NamespacedItemResolver.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Translation\\Translator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifierInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\PresenceVerifierInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Routing\\UrlGenerator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Optional.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\NullHandler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\Handler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\HandlerInterface.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Logger.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\ResettableInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\Logger.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\LogRecord.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\JsonSerializableDateTimeImmutable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\Context\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\Events\\MessageLogged.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\LogRecorder\\LogMessage.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\URL.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Schema.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ConfigurationUrlParser.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DetectsConcurrencyErrors.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DetectsLostConnections.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\ConnectionInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Grammars\\MySqlGrammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Grammars\\Grammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Grammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\CompilesJsonPaths.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseTransactionsManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Events\\ConnectionEstablished.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Events\\ConnectionEvent.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Grammars\\Grammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php',", "is_string": true}, {"message": "'\\app\\Models\\Asset.php',", "is_string": true}, {"message": "'\\app\\Models\\Depreciable.php',", "is_string": true}, {"message": "'\\app\\Models\\SnipeModel.php',", "is_string": true}, {"message": "'\\app\\Models\\CompanyableTrait.php',", "is_string": true}, {"message": "'\\app\\Models\\Traits\\HasUploads.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Factories\\HasFactory.php',", "is_string": true}, {"message": "'\\app\\Models\\Loggable.php',", "is_string": true}, {"message": "'\\app\\Models\\Requestable.php',", "is_string": true}, {"message": "'\\app\\Presenters\\Presentable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\SoftDeletes.php',", "is_string": true}, {"message": "'\\vendor\\watson\\validating\\src\\ValidatingTrait.php',", "is_string": true}, {"message": "'\\vendor\\watson\\validating\\src\\Injectors\\UniqueInjector.php',", "is_string": true}, {"message": "'\\app\\Http\\Traits\\UniqueUndeletedTrait.php',", "is_string": true}, {"message": "'\\app\\Models\\Traits\\Acceptable.php',", "is_string": true}, {"message": "'\\app\\Models\\Traits\\Searchable.php',", "is_string": true}, {"message": "'\\app\\Models\\CompanyableScope.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Scope.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\SoftDeletingScope.php',", "is_string": true}, {"message": "'\\vendor\\watson\\validating\\src\\ValidatingObserver.php',", "is_string": true}, {"message": "'\\app\\Observers\\AssetObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\User.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Authenticatable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Auth\\Access\\Authorizable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Passwords\\CanResetPassword.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\HasApiTokens.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Notifiable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\HasDatabaseNotifications.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\RoutesNotifications.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Authenticatable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Access\\Authorizable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\CanResetPassword.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Translation\\HasLocalePreference.php',", "is_string": true}, {"message": "'\\app\\Observers\\UserObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\Accessory.php',", "is_string": true}, {"message": "'\\app\\Observers\\AccessoryObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\Component.php',", "is_string": true}, {"message": "'\\app\\Observers\\ComponentObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\Consumable.php',", "is_string": true}, {"message": "'\\app\\Observers\\ConsumableObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\License.php',", "is_string": true}, {"message": "'\\app\\Observers\\LicenseObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\Setting.php',", "is_string": true}, {"message": "'\\app\\Observers\\SettingObserver.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Gate.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\HandlesAuthorization.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Access\\Gate.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Unit.php',", "is_string": true}, {"message": "'\\app\\Listeners\\LogListener.php',", "is_string": true}, {"message": "'\\app\\Listeners\\CheckoutableListener.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\RateLimiter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cache\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\FileStore.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RetrievesMultipleKeys.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cache\\Store.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cache\\LockProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cache\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\psr\\simple-cache\\src\\CacheInterface.php',", "is_string": true}, {"message": "'\\routes\\api.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResourceRegistrar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\PendingResourceRegistration.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Pluralizer.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\InflectorFactory.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Language.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\English\\InflectorFactory.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\GenericLanguageInflectorFactory.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\LanguageInflectorFactory.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\English\\Rules.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Ruleset.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Transformations.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\WordInflector.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\English\\Inflectible.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Transformation.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Pattern.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Patterns.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\English\\Uninflected.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Substitutions.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Substitution.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Word.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Inflector.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\CachedWordInflector.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\RulesetInflector.php',", "is_string": true}, {"message": "'\\routes\\web\\hardware.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Support\\ClosureScope.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Support\\ReflectionClosure.php',", "is_string": true}, {"message": "'\\routes\\web\\models.php',", "is_string": true}, {"message": "'\\routes\\web\\accessories.php',", "is_string": true}, {"message": "'\\routes\\web\\licenses.php',", "is_string": true}, {"message": "'\\routes\\web\\locations.php',", "is_string": true}, {"message": "'\\routes\\web\\consumables.php',", "is_string": true}, {"message": "'\\routes\\web\\fields.php',", "is_string": true}, {"message": "'\\routes\\web\\components.php',", "is_string": true}, {"message": "'\\routes\\web\\users.php',", "is_string": true}, {"message": "'\\routes\\web\\kits.php',", "is_string": true}, {"message": "'\\routes\\web.php',", "is_string": true}, {"message": "'\\app\\Livewire\\Importer.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Component.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Auth\\Access\\AuthorizesRequests.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Concerns\\InteractsWithProperties.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportEvents\\HandlesEvents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportRedirects\\HandlesRedirects.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportStreaming\\HandlesStreaming.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportAttributes\\HandlesAttributes.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportValidation\\HandlesValidation.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportFormObjects\\HandlesFormObjects.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportJsEvaluation\\HandlesJsEvaluation.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\HandlesDisablingBackButtonCache.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\HealthController.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\DashboardController.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\Controller.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bus\\DispatchesJobs.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\GroupsController.php',", "is_string": true}, {"message": "'\\routes\\scim.php',", "is_string": true}, {"message": "'\\vendor\\arietimmerman\\laravel-scim-server\\src\\RouteProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewFinderInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesFragments.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesLayouts.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesLoops.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesStacks.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesTranslations.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\View\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewName.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\App.php',", "is_string": true}, {"message": "'\\resources\\macros\\macros.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\FormFacade.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\HtmlServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\FormBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\Componentable.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\HtmlBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\FileSessionHandler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Session\\Session.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Breadcrumbs.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Manager.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Trail.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Registrar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Pipeline\\Pipeline.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\TrustProxies.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\NoSessionStore.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\Concerns\\ExcludesPaths.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\MaintenanceModeManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\FileBasedMaintenanceMode.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Foundation\\MaintenanceMode.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SymfonySessionDecorator.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\Session\\SessionInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ViewErrorBag.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckForSetup.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsWhereDateClauses.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ExplainsQueries.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Database\\Query\\Builder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Database\\Eloquent\\Builder.php',", "is_string": true}, {"message": "'\\app\\Models\\Company.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectorInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Events\\StatementPrepared.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Events\\QueryExecuted.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\QueryRecorder\\Query.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Collection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\InteractsWithDictionary.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Queue\\QueueableCollection.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckForDebug.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\CreatesUserProviders.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\StatefulGuard.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Guard.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\SupportsBasicAuth.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\UserProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Hashing\\HashServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Hashing\\HashManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Hashing\\Hasher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Timebox.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\CookieJar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cookie\\QueueingFactory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cookie\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Events\\Authenticated.php',", "is_string": true}, {"message": "'\\app\\Models\\Group.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\InteractsWithPivotTable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\JoinClause.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\TrimStrings.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\SecurityHeaders.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\PreventBackHistory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php',", "is_string": true}, {"message": "'\\vendor\\fruitcake\\php-cors\\src\\CorsService.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\IpUtils.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\LaravelDebugbar.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DebugBar.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\Support\\RequestIdGenerator.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\RequestIdGeneratorInterface.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\Storage\\FilesystemStorage.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\Storage\\StorageInterface.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\PhpInfoCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\DataCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\HasDataFormatter.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\HasXdebugLinks.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\DataCollectorInterface.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\Renderable.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\MessagesCollector.php',", "is_string": true}, {"message": "'\\vendor\\psr\\log\\src\\AbstractLogger.php',", "is_string": true}, {"message": "'\\vendor\\psr\\log\\src\\LoggerTrait.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\MessagesAggregateInterface.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\AssetProvider.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\TimeDataCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\DataFormatter.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\DataFormatterInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\error-handler\\ErrorRenderer\\FileLinkFormatter.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\error-handler\\ErrorRenderer\\ErrorRendererInterface.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\MemoryCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\ExceptionsCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\LaravelCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\EventCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataFormatter\\SimpleFormatter.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\ViewCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\RouteCollector.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\StreamHandler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\AbstractProcessingHandler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\AbstractHandler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\ProcessableHandlerTrait.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\FormattableHandlerTrait.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\ProcessableHandlerInterface.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\FormattableHandlerInterface.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Utils.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Formatter\\LineFormatter.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Formatter\\NormalizerFormatter.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Formatter\\FormatterInterface.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\QueryCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\PDO\\PDOCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataFormatter\\QueryFormatter.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\ObjectCountCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\LivewireCollector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\MailServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\Bridge\\Symfony\\SymfonyMailCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\LogsCollector.php',", "is_string": true}, {"message": "'\\vendor\\psr\\log\\src\\LogLevel.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\FilesCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\MultiAuthCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\GateCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\JavascriptRenderer.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\JavascriptRenderer.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Events\\Routing.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\routing\\Route.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\routing\\RouteCompiler.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\routing\\RouteCompilerInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\routing\\CompiledRoute.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\UriValidator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\ValidatorInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\MethodValidator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\SchemeValidator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\HostValidator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteParameterBinder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Events\\RouteMatched.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Contracts\\ControllerDispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\MiddlewareNameResolver.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\SortedMiddleware.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\EncryptCookies.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\VerifyCsrfToken.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckLocale.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckUserIsActivated.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckForTwoFactor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\AssetCountForSidebar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Session\\Middleware\\AuthenticatesSessions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Middleware\\AuthenticatesRequests.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\BreadcrumbsMiddleware.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\Encrypter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Encryption\\Encrypter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Encryption\\StringEncrypter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Encryption\\DecryptException.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\CookieValuePrefix.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php',", "is_string": true}, {"message": "'\\app\\Helpers\\Helper.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\ApiTokenCookieFactory.php',", "is_string": true}, {"message": "'\\app\\Models\\Statuslabel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\BelongsTo.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\ComparesRelatedModels.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\SupportsDefaultModels.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteSignatureParameters.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Support\\ClosureStream.php',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n            $trail->parent('hardware.index')\r\n                ->push(\\trans('general.bulkaudit'), \\route('asset.import-history'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n            $trail->parent('hardware.index')\r\n                ->push('Quickscan Checkin', \\route('hardware/quickscancheckin'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n            $trail->parent('hardware.index')\r\n                ->push(\\trans('admin/hardware/general.requested'), \\route('assets.requested'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n            $trail->parent('hardware.index')\r\n                ->push(\\trans_choice('general.audit_due_days', \\App\\Models\\Setting::getSettings()->audit_warning_days, ['days' => \\App\\Models\\Setting::getSettings()->audit_warning_days]), \\route('assets.audit.due'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n            $trail->parent('hardware.index')\r\n                ->push(\\trans_choice('general.checkin_due_days', \\App\\Models\\Setting::getSettings()->due_checkin_days, ['days' => \\App\\Models\\Setting::getSettings()->due_checkin_days]), \\route('assets.audit.due'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, \\App\\Models\\Asset $asset) =>\r\n            $trail->parent('hardware.show', $asset)\r\n                ->push(\\trans('general.audit'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n                $trail->parent('hardware.index')\r\n                ->push(\\trans('general.import-history'), \\route('asset.import-history'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, \\App\\Models\\Asset $asset) =>\r\n            $trail->parent('hardware.show', $asset)\r\n                ->push(\\trans('admin/hardware/general.checkout'), \\route('hardware.index'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, \\App\\Models\\Asset $asset) =>\r\n        $trail->parent('hardware.show', $asset)\r\n            ->push(\\trans('admin/hardware/general.checkin'), \\route('hardware.index'))\r\n        ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n            $trail->parent('hardware.index')\r\n                ->push(\\trans('admin/hardware/general.bulk_checkout'), \\route('hardware.index'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('models.index')\r\n            ->push(\\trans('admin/models/table.clone'), \\route('models.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, \\App\\Models\\License $license) =>\r\n        $trail->parent('licenses.show', $license)\r\n            ->push(\\trans('general.checkout'), \\route('licenses.checkout', $license))\r\n        ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, \\App\\Models\\LicenseSeat $licenseSeat) =>\r\n        $trail->parent('licenses.show', $licenseSeat->license)\r\n            ->push(\\trans('general.checkin'), \\route('licenses.checkin', $licenseSeat))\r\n        ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.backups'), \\route('kits.licenses.edit'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, \\App\\Models\\PredefinedKit $kit) =>\r\n        $trail->parent('kits.show', $kit)\r\n            ->push(\\trans('general.checkout'), \\route('kits.checkout.show', $kit))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.general_title'), \\route('settings.general.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.branding_title'), \\route('settings.branding.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.security_title'), \\route('settings.security.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.localization_title'), \\route('settings.localization.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.alert_title'), \\route('settings.alerts.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.webhook_title'), \\route('settings.slack.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.asset_tag_title'), \\route('settings.asset_tags.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.labels_title'), \\route('settings.labels.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.ldap_ad'), \\route('settings.ldap.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.php_info'), \\route('settings.phpinfo.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.oauth'), \\route('settings.oauth.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.google_login'), \\route('settings.google.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.purge'), \\route('settings.purge.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.login'), \\route('settings.logins.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.saml_title'), \\route('settings.saml.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n            $trail->parent('settings.index')\r\n                ->push(\\trans('admin/settings/general.backups'), \\route('settings.backups.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n    $trail->parent('home')\r\n        ->push(\\trans('general.import'), \\route('imports.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n                $trail->parent('home')\r\n            ->push(\\trans('general.editprofile'), \\route('profile'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.changepassword'), \\route('account.password.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.manage_api_keys'), \\route('user.api'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.viewassets'), \\route('view-assets'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.requested_assets_menu'), \\route('account.requested'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.requestable_items'), \\route('requestable-assets'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.accept_assets_menu'), \\route('account.accept'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, $id) =>\r\n        $trail->parent('account.accept')\r\n            ->push(\\trans('general.accept_item'), \\route('account.accept.item', $id))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.audit_report'), \\route('reports.audit'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.depreciation_report'), \\route('reports/depreciation'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.depreciation_report'), \\route('reports.audit'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.asset_maintenance_report'), \\route('reports/asset_maintenances'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.asset_maintenance_report'), \\route('reports/export/asset_maintenances'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.license_report'), \\route('reports/licenses'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.custom_report'), \\route('reports/custom'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, \\App\\Models\\ReportTemplate $reportTemplate) =>\r\n                $trail->parent('reports/custom')\r\n                    ->push($reportTemplate->name, null)\r\n                    ->push(\\trans('general.customize_report'), '')',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, \\App\\Models\\ReportTemplate $reportTemplate) =>\r\n                $trail->parent('reports/custom')\r\n                    ->push($reportTemplate->name, \\route('report-templates.show', $reportTemplate))\r\n                    ->push(\\trans('general.customize_report'), '')',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.activity_report'), \\route('reports.activity'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.unaccepted_asset_report'), \\route('reports/unaccepted_assets'))',", "is_string": true}, {"message": "'\\app\\Models\\LicenseSeat.php',", "is_string": true}, {"message": "'\\app\\Models\\CompanyableChildTrait.php',", "is_string": true}, {"message": "'\\app\\Models\\ICompanyableChild.php',", "is_string": true}, {"message": "'\\app\\Models\\CompanyableChildScope.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\View\\Engine.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Events\\PreparingResponse.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\Response.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\ResponseHeaderBag.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\ResponseTrait.php',", "is_string": true}, {"message": "'\\storage\\framework\\views\\634f41542304d78abb74aff576389081.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\general.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteUrlGenerator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\AnonymousComponent.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ComponentSlot.php',", "is_string": true}, {"message": "'\\storage\\framework\\views\\e42f7a353f1c45b64eeb8c8510b72bd0.php',", "is_string": true}, {"message": "'\\app\\Helpers\\IconHelper.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\DebugBarVarDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\Data.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\Stub.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\VarDumper\\DebugBarHtmlDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\Cursor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Events\\GateEvaluated.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Response.php',", "is_string": true}, {"message": "'\\storage\\framework\\views\\3cc4bcaaf43f1522cca5afa49210928a.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Mix.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\HtmlString.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\table.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\admin\\hardware\\general.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\admin\\hardware\\message.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\button.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\admin\\licenses\\general.php',", "is_string": true}, {"message": "'\\storage\\framework\\views\\603bcce3082fdd3035041e8b49695eb1.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Request.php',", "is_string": true}, {"message": "'\\app\\Models\\ConsumableAssignment.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasMany.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\SupportsInverseRelations.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Expression.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Database\\Query\\Expression.php',", "is_string": true}, {"message": "'\\app\\Models\\AccessoryCheckout.php',", "is_string": true}, {"message": "'\\app\\Models\\AssetModel.php',", "is_string": true}, {"message": "'\\app\\Http\\Traits\\TwoColumnUniqueUndeletedTrait.php',", "is_string": true}, {"message": "'\\app\\Presenters\\UserPresenter.php',", "is_string": true}, {"message": "'\\app\\Presenters\\Presenter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Storage.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Filesystem\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\UnixVisibility\\PortableVisibilityConverter.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\UnixVisibility\\VisibilityConverter.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem-local\\LocalFilesystemAdapter.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\FilesystemAdapter.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\ChecksumProvider.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\PathPrefixer.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem-local\\FallbackMimeTypeDetector.php',", "is_string": true}, {"message": "'\\vendor\\league\\mime-type-detection\\src\\MimeTypeDetector.php',", "is_string": true}, {"message": "'\\vendor\\league\\mime-type-detection\\src\\FinfoMimeTypeDetector.php',", "is_string": true}, {"message": "'\\vendor\\league\\mime-type-detection\\src\\ExtensionLookup.php',", "is_string": true}, {"message": "'\\vendor\\league\\mime-type-detection\\src\\GeneratedExtensionToMimeTypeMap.php',", "is_string": true}, {"message": "'\\vendor\\league\\mime-type-detection\\src\\ExtensionToMimeTypeMap.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\Visibility.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\LocalFilesystemAdapter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Filesystem\\Cloud.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Filesystem\\Filesystem.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\Filesystem.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\CalculateChecksumFromStream.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\FilesystemOperator.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\FilesystemReader.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\FilesystemWriter.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\Config.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\WhitespacePathNormalizer.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\PathNormalizer.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\admin\\custom_fields\\general.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Crumb.php',", "is_string": true}, {"message": "'\\storage\\framework\\views\\3b1b16c575e20bc87c056cbfccd78ad8.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\MessageBag.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\MessageBag.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\MessageProvider.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\datepicker.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\validation.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Session.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Events\\ResponsePrepared.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\SymfonyHttpDriver.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\HttpDriverInterface.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\SessionCollector.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\uid\\Ulid.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\uid\\AbstractUid.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\uid\\HashableInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\uid\\TimeBasedUidInterface.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\RequestCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\Support\\Clockwork\\ClockworkCollector.php',", "is_string": true}], "count": 1060}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"mgpiattos\"\n  \"user\" => array:43 [\n    \"id\" => 1\n    \"email\" => \"<EMAIL>\"\n    \"activated\" => 1\n    \"created_by\" => null\n    \"activated_at\" => null\n    \"last_login\" => \"2025-08-16 08:39:49\"\n    \"first_name\" => \"<PERSON>\"\n    \"last_name\" => \"Piattos\"\n    \"created_at\" => \"2025-08-16T08:39:28.000000Z\"\n    \"updated_at\" => \"2025-08-16T08:39:49.000000Z\"\n    \"deleted_at\" => null\n    \"website\" => null\n    \"country\" => null\n    \"gravatar\" => null\n    \"location_id\" => null\n    \"phone\" => null\n    \"jobtitle\" => null\n    \"manager_id\" => null\n    \"employee_num\" => null\n    \"avatar\" => null\n    \"username\" => \"mgpiattos\"\n    \"notes\" => null\n    \"company_id\" => null\n    \"ldap_import\" => 0\n    \"locale\" => \"en-US\"\n    \"show_in_list\" => 1\n    \"two_factor_enrolled\" => 0\n    \"two_factor_optin\" => 0\n    \"department_id\" => null\n    \"address\" => null\n    \"city\" => null\n    \"state\" => null\n    \"zip\" => null\n    \"skin\" => null\n    \"remote\" => 0\n    \"start_date\" => null\n    \"end_date\" => null\n    \"scim_externalid\" => null\n    \"autoassign_licenses\" => 1\n    \"vip\" => 0\n    \"enable_sounds\" => 0\n    \"enable_confetti\" => 0\n    \"groups\" => []\n  ]\n]", "api": "null"}, "names": "web: mgpiattos"}, "gate": {"count": 54, "messages": [{"message": "[\n  ability => create,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.80206, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\License,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\License]\n]", "message_html": "<pre class=sf-dump id=sf-dump-327326312 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\License</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\License</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\License]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-327326312\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.803036, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Accessory,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Accessory]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Accessory</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Accessory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Accessory]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.803636, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Consumable,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Consumable]\n]", "message_html": "<pre class=sf-dump id=sf-dump-462859413 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Consumable</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Consumable</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\Consumable]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-462859413\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.803894, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Component,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Component]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1311832580 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Component</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Component</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Component]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1311832580\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.804086, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1855629845 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1855629845\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.80426, "xdebug_link": null}, {"message": "[\n  ability => index,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>index App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.824219, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\License,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\License]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\License</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\License</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\License]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.825539, "xdebug_link": null}, {"message": "[\n  ability => index,\n  target => App\\Models\\Accessory,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Accessory]\n]", "message_html": "<pre class=sf-dump id=sf-dump-341028162 data-indent-pad=\"  \"><span class=sf-dump-note>index App\\Models\\Accessory</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Accessory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Accessory]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-341028162\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.826397, "xdebug_link": null}, {"message": "[\n  ability => index,\n  target => App\\Models\\Consumable,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Consumable]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1983683165 data-indent-pad=\"  \"><span class=sf-dump-note>index App\\Models\\Consumable</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Consumable</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\Consumable]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1983683165\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.827235, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Component,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Component]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1881356643 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Component</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Component</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Component]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1881356643\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.827873, "xdebug_link": null}, {"message": "[\n  ability => index,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-873651850 data-indent-pad=\"  \"><span class=sf-dump-note>index App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-873651850\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.828564, "xdebug_link": null}, {"message": "[ability => admin, target => null, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1259738516 data-indent-pad=\"  \"><span class=sf-dump-note>admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1259738516\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.829168, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.829339, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\License,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\License]\n]", "message_html": "<pre class=sf-dump id=sf-dump-406839177 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\License</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\License</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\License]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-406839177\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.829989, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Accessory,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Accessory]\n]", "message_html": "<pre class=sf-dump id=sf-dump-510972559 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Accessory</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Accessory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Accessory]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-510972559\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.830614, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Consumable,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Consumable]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1697947227 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Consumable</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Consumable</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\Consumable]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1697947227\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.831247, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Component,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Component]\n]", "message_html": "<pre class=sf-dump id=sf-dump-146220704 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Component</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Component</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Component]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-146220704\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.831993, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-188991306 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-188991306\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.832672, "xdebug_link": null}, {"message": "[ability => admin, target => null, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1495340625 data-indent-pad=\"  \"><span class=sf-dump-note>admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1495340625\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.833323, "xdebug_link": null}, {"message": "[\n  ability => superadmin,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2019917558 data-indent-pad=\"  \"><span class=sf-dump-note>superadmin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">superadmin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2019917558\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.852038, "xdebug_link": null}, {"message": "[\n  ability => viewRequestable,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-805315505 data-indent-pad=\"  \"><span class=sf-dump-note>viewRequestable App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">viewRequestable</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-805315505\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.864048, "xdebug_link": null}, {"message": "[\n  ability => self.profile,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1729448904 data-indent-pad=\"  \"><span class=sf-dump-note>self.profile </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">self.profile</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1729448904\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.865344, "xdebug_link": null}, {"message": "[\n  ability => self.api,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1965058003 data-indent-pad=\"  \"><span class=sf-dump-note>self.api </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">self.api</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1965058003\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.866434, "xdebug_link": null}, {"message": "[\n  ability => superadmin,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-107694499 data-indent-pad=\"  \"><span class=sf-dump-note>superadmin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">superadmin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-107694499\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.867499, "xdebug_link": null}, {"message": "[ability => admin, target => null, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-6753954 data-indent-pad=\"  \"><span class=sf-dump-note>admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-6753954\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.868471, "xdebug_link": null}, {"message": "[\n  ability => index,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-365568432 data-indent-pad=\"  \"><span class=sf-dump-note>index App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-365568432\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.869088, "xdebug_link": null}, {"message": "[\n  ability => audit,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-952697292 data-indent-pad=\"  \"><span class=sf-dump-note>audit App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">audit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-952697292\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.876646, "xdebug_link": null}, {"message": "[\n  ability => checkin,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-188316070 data-indent-pad=\"  \"><span class=sf-dump-note>checkin App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">checkin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-188316070\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.877355, "xdebug_link": null}, {"message": "[\n  ability => checkin,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2053012978 data-indent-pad=\"  \"><span class=sf-dump-note>checkin App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">checkin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2053012978\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.878037, "xdebug_link": null}, {"message": "[\n  ability => checkout,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-809163156 data-indent-pad=\"  \"><span class=sf-dump-note>checkout App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">checkout</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-809163156\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.878309, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-24054556 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-24054556\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.878604, "xdebug_link": null}, {"message": "[ability => admin, target => null, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1611391899 data-indent-pad=\"  \"><span class=sf-dump-note>admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1611391899\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.878868, "xdebug_link": null}, {"message": "[\n  ability => audit,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>audit App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">audit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.879093, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\License,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\License]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\License</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\License</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\License]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.87933, "xdebug_link": null}, {"message": "[\n  ability => index,\n  target => App\\Models\\Accessory,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Accessory]\n]", "message_html": "<pre class=sf-dump id=sf-dump-31297244 data-indent-pad=\"  \"><span class=sf-dump-note>index App\\Models\\Accessory</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Accessory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Accessory]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-31297244\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.879953, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Consumable,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Consumable]\n]", "message_html": "<pre class=sf-dump id=sf-dump-727057081 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Consumable</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Consumable</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\Consumable]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-727057081\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.880549, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Component,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Component]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1763613651 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Component</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Component</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Component]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1763613651\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.881124, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\PredefinedKit,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PredefinedKit]\n]", "message_html": "<pre class=sf-dump id=sf-dump-878071828 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\PredefinedKit</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\PredefinedKit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\PredefinedKit]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-878071828\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.881685, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-459949604 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-459949604\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.882417, "xdebug_link": null}, {"message": "[ability => import, target => null, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-521350107 data-indent-pad=\"  \"><span class=sf-dump-note>import </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">import</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-521350107\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.882994, "xdebug_link": null}, {"message": "[\n  ability => backend.interact,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-475371719 data-indent-pad=\"  \"><span class=sf-dump-note>backend.interact </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">backend.interact</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-475371719\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.883596, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\CustomField,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\CustomField]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1754400554 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\CustomField</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\CustomField</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\CustomField]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1754400554\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.884482, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Statuslabel,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Statuslabel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-43179745 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Statuslabel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Statuslabel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\Statuslabel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-43179745\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.88523, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\AssetModel,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AssetModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1846190168 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\AssetModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\AssetModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\AssetModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1846190168\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.885471, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Category,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Category]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1615462931 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Category</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"19 characters\">App\\Models\\Category</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; App\\Models\\Category]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1615462931\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.885699, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Manufacturer,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Manufacturer]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1276526258 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Manufacturer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Manufacturer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\Manufacturer]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1276526258\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.885905, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Supplier,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Supplier]\n]", "message_html": "<pre class=sf-dump id=sf-dump-494893860 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Supplier</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"19 characters\">App\\Models\\Supplier</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; App\\Models\\Supplier]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-494893860\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.886105, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Department,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Department]\n]", "message_html": "<pre class=sf-dump id=sf-dump-314766547 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Department</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Department</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\Department]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-314766547\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.886326, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Location,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Location]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Location</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"19 characters\">App\\Models\\Location</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; App\\Models\\Location]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.886519, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Company,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Company]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Company</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Company</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Company]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.886705, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Depreciation,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Depreciation]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Depreciation</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Depreciation</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\Depreciation]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.886892, "xdebug_link": null}, {"message": "[\n  ability => reports.view,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-887487436 data-indent-pad=\"  \"><span class=sf-dump-note>reports.view </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">reports.view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-887487436\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.887073, "xdebug_link": null}, {"message": "[\n  ability => viewRequestable,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1794529883 data-indent-pad=\"  \"><span class=sf-dump-note>viewRequestable App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">viewRequestable</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1794529883\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.888398, "xdebug_link": null}]}, "session": {"url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/_debugbar/open?id=01K2S0M0MXW8VEK11S1RHT35BF&op=get\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_token": "XcFb6uBj6sHVSCD8PzSZfpkew7wBekXM9V2bT1eX", "backUrl": "http://127.0.0.1:8000/login", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "$2y$10$2Y6DCYiWr0S5QSGbJatyfuYNCX1.Ib9MeBtmJ22fHJVGhcCwvfQ3G", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000", "action_name": "home", "controller_action": "\\App\\Http\\Controllers\\DashboardController@index", "uri": "GET /", "controller": "\\App\\Http\\Controllers\\DashboardController@index<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=27\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=27\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/DashboardController.php:27-53</a>", "middleware": "web, auth, breadcrumbs", "duration": "870ms", "peak_memory": "42MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-709224517 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-709224517\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1771709162 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1771709162\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1284432217 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Microsoft Edge&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1078 characters\">csrftoken=eRZeQ4hlaHT8dltJRqLktclcixjXegkX; snipeit_session=Q20TsmwTLpe7V4NLPLzu7UJvMbqL6bJ6wfpUffVy; snipeit_passport_token=eyJpdiI6Ik9MZG1ieXYrY2FkWDBuMFp4TG9uaEE9PSIsInZhbHVlIjoic3I5TVhJRkJrWXd6OGZSZ3ZlQllXN3lnS21RT2tpby9KdC81Y0RtZWtMajJETGlQNUJhM3FId1JDNHpnM0hHY0g4WHdDQ2lBSkswSjdHZWpUMDRuUGFmREhnSmZ2V3ZockpSY1BITWltNHIrYnZ4QUVTVlh2ZGdlc2Y4MFQ0bnIvVDV6QUMxaHRSblYyeTBvUHdzaEVkRXI2ZlJ0TUNhR0Zja25ySzhDWFRna2E4eDN5M0VjdnJXODZWR3drQ0tUR3lwSHdvU3hvVXdJaVE1SUhIdTEzWXQ2aTlUL3lJd3Y4eCs3aTU2aHpoeHc4ejU5WWh3cWlvaHRtOTFYOEhtRDBOSDU2NS9wSHdPRWFmWEVqUCthL01nQ2lhWkFJdGhYQStGVDJaOXkxUnpHZWZEb0l6REZLazVsNjRDMEtFK0EiLCJtYWMiOiJkMzM0ZGMwZTgxOWNhM2FjNzE1OWJiOTkxN2ZkZjE4NTRjYTExYWZhYWQ4NDg2MTU2OGY3YmViYjVjYTE4MzMzIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InhJZ3VFb0M4dXcyaC9YQUVGeThUVnc9PSIsInZhbHVlIjoiMnZJcGRRajJ3OFBNMThCeFdaVlVnSHdmZ0toMnJTeVlhZFhGWFNHc1gvajhnTDVueEJzRTk4T3NKQzNmbU1wYjh3Z28vVXIvT1dWMXFqZ1JFS2pJalY1RnYrV1IydEZ3eVg5bS9uZEN5dWZRek1pRUZxUUs3T0l6SFNZdkFDekMiLCJtYWMiOiI2YjIzYTY5OWI3MjYyNTk1MmRmMjE0YjUxMDEzMGNlOGIwYWE4MDBlODBiYTQwMzE0MTBlN2EyMzIyOWVjN2M5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1284432217\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1836996069 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>csrftoken</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>snipeit_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>snipeit_passport_token</span>\" => \"<span class=sf-dump-str title=\"187 characters\">eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOjEsImNzcmYiOiJYY0ZiNnVCajZzSFZTQ0Q4UHpTWmZwa2V3N3dCZWtYTTlWMmJUMWVYIiwiZXhwaXJ5IjoxNzU2MDUzNjUzfQ.pjRhofmn12fl4QJTEhqcaBv_ZQ7OQvwBa3rZrG_GQ-g</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XcFb6uBj6sHVSCD8PzSZfpkew7wBekXM9V2bT1eX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1836996069\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 16 Aug 2025 08:41:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-clockwork-id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">01K2S0MSPGWA004697KECFXBRR</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-clockwork-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-clockwork-path</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">_debugbar/clockwork/</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-806717277 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"73 characters\">http://127.0.0.1:8000/_debugbar/open?id=01K2S0M0MXW8VEK11S1RHT35BF&amp;op=get</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XcFb6uBj6sHVSCD8PzSZfpkew7wBekXM9V2bT1eX</span>\"\n  \"<span class=sf-dump-key>backUrl</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$2Y6DCYiWr0S5QSGbJatyfuYNCX1.Ib9MeBtmJ22fHJVGhcCwvfQ3G</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-806717277\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000", "action_name": "home", "controller_action": "\\App\\Http\\Controllers\\DashboardController@index"}, "badge": null}, "clockwork": {"getData": [], "postData": [], "headers": {"host": ["127.0.0.1:8000"], "connection": ["keep-alive"], "sec-ch-ua": ["\"Not;A=Brand\";v=\"99\", \"Microsoft Edge\";v=\"139\", \"Chromium\";v=\"139\""], "sec-ch-ua-mobile": ["?0"], "sec-ch-ua-platform": ["\"Windows\""], "upgrade-insecure-requests": ["1"], "user-agent": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"], "accept": ["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"], "sec-fetch-site": ["none"], "sec-fetch-mode": ["navigate"], "sec-fetch-user": ["?1"], "sec-fetch-dest": ["document"], "accept-encoding": ["gzip, deflate, br, zstd"], "accept-language": ["en-US,en;q=0.9"], "cookie": ["csrftoken=eRZeQ4hlaHT8dltJRqLktclcixjXegkX; snipeit_session=Q20TsmwTLpe7V4NLPLzu7UJvMbqL6bJ6wfpUffVy; snipeit_passport_token=eyJpdiI6Ik9MZG1ieXYrY2FkWDBuMFp4TG9uaEE9PSIsInZhbHVlIjoic3I5TVhJRkJrWXd6OGZSZ3ZlQllXN3lnS21RT2tpby9KdC81Y0RtZWtMajJETGlQNUJhM3FId1JDNHpnM0hHY0g4WHdDQ2lBSkswSjdHZWpUMDRuUGFmREhnSmZ2V3ZockpSY1BITWltNHIrYnZ4QUVTVlh2ZGdlc2Y4MFQ0bnIvVDV6QUMxaHRSblYyeTBvUHdzaEVkRXI2ZlJ0TUNhR0Zja25ySzhDWFRna2E4eDN5M0VjdnJXODZWR3drQ0tUR3lwSHdvU3hvVXdJaVE1SUhIdTEzWXQ2aTlUL3lJd3Y4eCs3aTU2aHpoeHc4ejU5WWh3cWlvaHRtOTFYOEhtRDBOSDU2NS9wSHdPRWFmWEVqUCthL01nQ2lhWkFJdGhYQStGVDJaOXkxUnpHZWZEb0l6REZLazVsNjRDMEtFK0EiLCJtYWMiOiJkMzM0ZGMwZTgxOWNhM2FjNzE1OWJiOTkxN2ZkZjE4NTRjYTExYWZhYWQ4NDg2MTU2OGY3YmViYjVjYTE4MzMzIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InhJZ3VFb0M4dXcyaC9YQUVGeThUVnc9PSIsInZhbHVlIjoiMnZJcGRRajJ3OFBNMThCeFdaVlVnSHdmZ0toMnJTeVlhZFhGWFNHc1gvajhnTDVueEJzRTk4T3NKQzNmbU1wYjh3Z28vVXIvT1dWMXFqZ1JFS2pJalY1RnYrV1IydEZ3eVg5bS9uZEN5dWZRek1pRUZxUUs3T0l6SFNZdkFDekMiLCJtYWMiOiI2YjIzYTY5OWI3MjYyNTk1MmRmMjE0YjUxMDEzMGNlOGIwYWE4MDBlODBiYTQwMzE0MTBlN2EyMzIyOWVjN2M5IiwidGFnIjoiIn0%3D"]}, "cookies": {"csrftoken": null, "snipeit_session": null, "snipeit_passport_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOjEsImNzcmYiOiJYY0ZiNnVCajZzSFZTQ0Q4UHpTWmZwa2V3N3dCZWtYTTlWMmJUMWVYIiwiZXhwaXJ5IjoxNzU2MDUzNjUzfQ.pjRhofmn12fl4QJTEhqcaBv_ZQ7OQvwBa3rZrG_GQ-g", "XSRF-TOKEN": "XcFb6uBj6sHVSCD8PzSZfpkew7wBekXM9V2bT1eX"}, "uri": "/", "method": "GET", "responseStatus": 200, "sessionData": {"url": [], "_previous": {"url": "http://127.0.0.1:8000/_debugbar/open?id=01K2S0M0MXW8VEK11S1RHT35BF&op=get"}, "_flash": {"old": [], "new": []}, "_token": "XcFb6uBj6sHVSCD8PzSZfpkew7wBekXM9V2bT1eX", "backUrl": "http://127.0.0.1:8000/login", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": 1, "password_hash_web": "$2y$10$2Y6DCYiWr0S5QSGbJatyfuYNCX1.Ib9MeBtmJ22fHJVGhcCwvfQ3G", "PHPDEBUGBAR_STACK_DATA": []}}}