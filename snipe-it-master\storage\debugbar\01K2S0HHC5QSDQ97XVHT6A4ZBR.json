{"__meta": {"id": "01K2S0HHC5QSDQ97XVHT6A4ZBR", "datetime": "2025-08-16 08:39:36", "utime": **********.072564, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[08:39:33] LOG.debug: SAML page requested, but samlData seems empty.", "message_html": null, "is_string": false, "label": "debug", "time": **********.890501, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 16, "start": **********.146174, "end": **********.072656, "duration": 3.9264819622039795, "duration_str": "3.93s", "measures": [{"label": "Booting", "start": **********.146174, "relative_start": 0, "end": **********.87344, "relative_end": **********.87344, "duration": 0.****************, "duration_str": "727ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.873473, "relative_start": 0.****************, "end": **********.072664, "relative_end": 8.106231689453125e-06, "duration": 3.***************, "duration_str": "3.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.255366, "relative_start": 1.***************, "end": **********.272745, "relative_end": **********.272745, "duration": 0.017378807067871094, "duration_str": "17.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "select count(*) as aggregate from `assets` where `assets`.`deleted_at` is null", "start": **********.78561, "relative_start": 1.****************, "end": **********.78723, "relative_end": **********.78723, "duration": 0.0016200542449951172, "duration_str": "1.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets...", "start": **********.798453, "relative_start": 1.****************, "end": **********.800303, "relative_end": **********.800303, "duration": 0.0018498897552490234, "duration_str": "1.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`assigned_to` is null and exists (select *...", "start": **********.8070211, "relative_start": 1.6608471870422363, "end": **********.808701, "relative_end": **********.808701, "duration": 0.0016798973083496094, "duration_str": "1.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assigned_to` > ? and `assets`.`deleted_at` is null", "start": **********.81363, "relative_start": 1.6674561500549316, "end": **********.81517, "relative_end": **********.81517, "duration": 0.0015399456024169922, "duration_str": "1.54ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets...", "start": **********.821467, "relative_start": 1.67529296875, "end": **********.822907, "relative_end": **********.822907, "duration": 0.0014400482177734375, "duration_str": "1.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets...", "start": **********.829299, "relative_start": 1.6831250190734863, "end": **********.831089, "relative_end": **********.831089, "duration": 0.0017900466918945312, "duration_str": "1.79ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets...", "start": **********.8398728, "relative_start": 1.6936988830566406, "end": **********.841333, "relative_end": **********.841333, "duration": 0.0014600753784179688, "duration_str": "1.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `byod` = ? and `assets`.`deleted_at` is null", "start": **********.847348, "relative_start": 1.701174020767212, "end": **********.848828, "relative_end": **********.848828, "duration": 0.0014801025390625, "duration_str": "1.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`next_audit_date` is not null and `assets`...", "start": **********.855136, "relative_start": 1.7089619636535645, "end": **********.856586, "relative_end": **********.856586, "duration": 0.0014500617980957031, "duration_str": "1.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`next_audit_date` is not null and `assets`...", "start": **********.86324, "relative_start": 1.7170660495758057, "end": **********.86513, "relative_end": **********.86513, "duration": 0.001889944076538086, "duration_str": "1.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`expected_checkin` is not null and `assets...", "start": **********.872206, "relative_start": 1.726032018661499, "end": **********.873806, "relative_end": **********.873806, "duration": 0.001600027084350586, "duration_str": "1.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`expected_checkin` is not null and `assets...", "start": **********.879535, "relative_start": 1.733361005783081, "end": **********.881355, "relative_end": **********.881355, "duration": 0.0018200874328613281, "duration_str": "1.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "Preparing Response", "start": **********.896713, "relative_start": 1.7505390644073486, "end": **********.063306, "relative_end": **********.063306, "duration": 2.166593074798584, "duration_str": "2.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.2.12", "Environment": "production", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "UTC", "Locale": "en-US"}}, "event": {"count": 42, "start": **********.146174, "end": **********.072941, "duration": 3.***************, "duration_str": "3.93s", "measures": [{"label": "Illuminate\\Routing\\Events\\Routing", "start": **********.255388, "relative_start": 1.****************, "end": **********.255388, "relative_end": **********.255388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\Routing"}, {"label": "Illuminate\\Routing\\Events\\RouteMatched", "start": **********.272793, "relative_start": 1.****************, "end": **********.272793, "relative_end": **********.272793, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\RouteMatched"}, {"label": "Illuminate\\Foundation\\Events\\LocaleUpdated", "start": **********.783596, "relative_start": 1.****************, "end": **********.783596, "relative_end": **********.783596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Foundation\\Events\\LocaleUpdated"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.78623, "relative_start": 1.****************, "end": **********.78623, "relative_end": **********.78623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.79214, "relative_start": 1.****************, "end": **********.79214, "relative_end": **********.79214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "eloquent.booting: App\\Models\\Statuslabel", "start": **********.794685, "relative_start": 1.6485109329223633, "end": **********.794685, "relative_end": **********.794685, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booting"}, {"label": "eloquent.booted: App\\Models\\Statuslabel", "start": **********.795042, "relative_start": 1.6488680839538574, "end": **********.795042, "relative_end": **********.795042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.799137, "relative_start": 1.6529631614685059, "end": **********.799137, "relative_end": **********.799137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.805661, "relative_start": 1.659487009048462, "end": **********.805661, "relative_end": **********.805661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.80757, "relative_start": 1.6613960266113281, "end": **********.80757, "relative_end": **********.80757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.812866, "relative_start": 1.6666920185089111, "end": **********.812866, "relative_end": **********.812866, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.814158, "relative_start": 1.6679840087890625, "end": **********.814158, "relative_end": **********.814158, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.820265, "relative_start": 1.674091100692749, "end": **********.820265, "relative_end": **********.820265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.822013, "relative_start": 1.6758389472961426, "end": **********.822013, "relative_end": **********.822013, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.827491, "relative_start": 1.6813170909881592, "end": **********.827491, "relative_end": **********.827491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.829999, "relative_start": 1.6838250160217285, "end": **********.829999, "relative_end": **********.829999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.838414, "relative_start": 1.6922399997711182, "end": **********.838414, "relative_end": **********.838414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.840466, "relative_start": 1.6942920684814453, "end": **********.840466, "relative_end": **********.840466, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.846598, "relative_start": 1.7004239559173584, "end": **********.846598, "relative_end": **********.846598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.847934, "relative_start": 1.7017600536346436, "end": **********.847934, "relative_end": **********.847934, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.853531, "relative_start": 1.7073569297790527, "end": **********.853531, "relative_end": **********.853531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.855615, "relative_start": 1.7094409465789795, "end": **********.855615, "relative_end": **********.855615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.861901, "relative_start": 1.7157270908355713, "end": **********.861901, "relative_end": **********.861901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.8641, "relative_start": 1.717926025390625, "end": **********.8641, "relative_end": **********.8641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.870537, "relative_start": 1.724363088607788, "end": **********.870537, "relative_end": **********.870537, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.87287, "relative_start": 1.7266960144042969, "end": **********.87287, "relative_end": **********.87287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.878167, "relative_start": 1.7319929599761963, "end": **********.878167, "relative_end": **********.878167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.880259, "relative_start": 1.7340850830078125, "end": **********.880259, "relative_end": **********.880259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.886458, "relative_start": 1.7402839660644531, "end": **********.886458, "relative_end": **********.886458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Log\\Events\\MessageLogged", "start": **********.89107, "relative_start": 1.7448959350585938, "end": **********.89107, "relative_end": **********.89107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Log\\Events\\MessageLogged"}, {"label": "creating: auth.login", "start": **********.89613, "relative_start": 1.****************, "end": **********.89613, "relative_end": **********.89613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "Illuminate\\Routing\\Events\\PreparingResponse", "start": **********.896723, "relative_start": 1.***************, "end": **********.896723, "relative_end": **********.896723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\PreparingResponse"}, {"label": "composing: auth.login", "start": **********.901801, "relative_start": 1.***************, "end": **********.901801, "relative_end": **********.901801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: notifications", "start": **********.151771, "relative_start": 3.****************, "end": **********.151771, "relative_end": **********.151771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: notifications", "start": **********.151868, "relative_start": 3.****************, "end": **********.151868, "relative_end": **********.151868, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.049362, "relative_start": 3.9031879901885986, "end": **********.049362, "relative_end": **********.049362, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.049446, "relative_start": 3.9032721519470215, "end": **********.049446, "relative_end": **********.049446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.052948, "relative_start": 3.9067740440368652, "end": **********.052948, "relative_end": **********.052948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.052988, "relative_start": 3.9068140983581543, "end": **********.052988, "relative_end": **********.052988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: layouts.basic", "start": **********.05634, "relative_start": 3.9101660********, "end": **********.05634, "relative_end": **********.05634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: layouts.basic", "start": **********.056433, "relative_start": 3.****************, "end": **********.056433, "relative_end": **********.056433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Routing\\Events\\ResponsePrepared", "start": **********.063341, "relative_start": 3.****************, "end": **********.063341, "relative_end": **********.063341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\ResponsePrepared"}], "nb_measures": 42}, "views": {"count": 5, "nb_templates": 5, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.902459, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x notifications", "param_count": null, "params": [], "start": **********.152336, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/notifications.blade.phpnotifications", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fnotifications.blade.php&line=1", "ajax": false, "filename": "notifications.blade.php", "line": "?"}, "render_count": 1, "name_original": "notifications"}, {"name": "2x 2548ffbf4d41f5cc86ea949d4376e84f::icon", "param_count": null, "params": [], "start": **********.04997, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Providers/../../resources/views/blade/icon.blade.php2548ffbf4d41f5cc86ea949d4376e84f::icon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fblade%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}, "render_count": 2, "name_original": "2548ffbf4d41f5cc86ea949d4376e84f::icon"}, {"name": "1x layouts.basic", "param_count": null, "params": [], "start": **********.056999, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/basic.blade.phplayouts.basic", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Flayouts%2Fbasic.blade.php&line=1", "ajax": false, "filename": "basic.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.basic"}]}, "route": {"uri": "GET login", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\LoginController@showLoginForm<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FAuth%2FLoginController.php&line=63\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FAuth%2FLoginController.php&line=63\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Auth/LoginController.php:63-89</a>"}, "queries": {"count": 12, "nb_statements": 12, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01962, "accumulated_duration_str": "19.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `assets` where `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 37}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 21, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.78561, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:37", "source": {"index": 19, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=37", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "37"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `deployable` = 0 and `pending` = 0 and `archived` = 1 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0, 0, 1], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.798453, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:39", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=39", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "39"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`assigned_to` is null and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `deployable` = 1 and `pending` = 0 and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.8070211, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:47", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=47", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "47"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assigned_to` > '0' and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["0"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.81363, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:54", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=54", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "54"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `deployable` = 0 and `pending` = 0 and `archived` = 1 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0, 0, 1], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 61}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.821467, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:61", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=61", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "61"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `deployable` = 0 and `pending` = 1 and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0, 1, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 68}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.829299, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:68", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=68", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "68"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `deployable` = 0 and `pending` = 0 and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0, 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 75}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.8398728, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:75", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=75", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "75"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `byod` = '1' and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 82}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.847348, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:82", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 82}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=82", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "82"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`next_audit_date` is not null and `assets`.`next_audit_date` between '2025-08-16' and '2025-08-16' and `assets`.`archived` = 0 and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16", "2025-08-16", 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 89}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.855136, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:89", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=89", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "89"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`next_audit_date` is not null and `assets`.`next_audit_date` < '2025-08-16' and `assets`.`archived` = 0 and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16", 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 96}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.86324, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:96", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=96", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "96"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`expected_checkin` is not null and `assets`.`expected_checkin` between '2025-08-16' and '2025-08-16' and `assets`.`archived` = 0 and `assets`.`assigned_to` is not null and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16", "2025-08-16", 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 103}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.872206, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:103", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=103", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "103"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`expected_checkin` is not null and `assets`.`expected_checkin` < '2025-08-16' and `assets`.`archived` = 0 and `assets`.`assigned_to` is not null and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16", 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 110}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.879535, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:110", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=110", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "110"}, "connection": "snipeit", "explain": null}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "logs": {"count": 0, "messages": []}, "files": {"messages": [{"message": "'\\server.php',", "is_string": true}, {"message": "'\\public\\index.php',", "is_string": true}, {"message": "'\\vendor\\autoload.php',", "is_string": true}, {"message": "'\\vendor\\composer\\autoload_real.php',", "is_string": true}, {"message": "'\\vendor\\composer\\platform_check.php',", "is_string": true}, {"message": "'\\vendor\\composer\\ClassLoader.php',", "is_string": true}, {"message": "'\\vendor\\composer\\autoload_static.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\deprecation-contracts\\function.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-mbstring\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-mbstring\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-normalizer\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-normalizer\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-ctype\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-ctype\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-grapheme\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-grapheme\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\string\\Resources\\functions.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-php83\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-php83\\bootstrap81.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Resources\\functions\\dump.php',", "is_string": true}, {"message": "'\\vendor\\ralouphie\\getallheaders\\src\\getallheaders.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-php80\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-idn\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-idn\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\react\\promise\\src\\functions_include.php',", "is_string": true}, {"message": "'\\vendor\\react\\promise\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\functions_include.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\prompts\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\termwind\\src\\Functions.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\clock\\Resources\\now.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-uuid\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-uuid\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Resources\\functions.php',", "is_string": true}, {"message": "'\\vendor\\ramsey\\uuid\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\fakerphp\\faker\\src\\Faker\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\mtdowling\\jmespath.php\\src\\JmesPath.php',", "is_string": true}, {"message": "'\\vendor\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock\\autoload.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\php-text-template\\src\\Template.php',", "is_string": true}, {"message": "'\\vendor\\phpseclib\\phpseclib\\phpseclib\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\flare-client-php\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-php81\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\aws\\aws-sdk-php\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\ezyang\\htmlpurifier\\library\\HTMLPurifier.composer.php',", "is_string": true}, {"message": "'\\vendor\\phpstan\\phpstan\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php',", "is_string": true}, {"message": "'\\vendor\\psy\\psysh\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\helpers\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\league\\csv\\src\\functions_include.php',", "is_string": true}, {"message": "'\\vendor\\league\\csv\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\mockery\\mockery\\library\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\mockery\\mockery\\library\\Mockery.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\collision\\src\\Adapters\\Phpunit\\Autoload.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Runner\\Version.php',", "is_string": true}, {"message": "'\\vendor\\sebastian\\version\\src\\Version.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\collision\\src\\Adapters\\Phpunit\\Subscribers\\EnsurePrinterIsRegisteredSubscriber.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Event\\Events\\Application\\StartedSubscriber.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Event\\Subscriber.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\autoload.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\autoload-php7.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\src\\Compat.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\namespaced.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\sodium_compat.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\constants.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\php84compat_const.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\php84compat.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\stream-xchacha20.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\ristretto255.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock-phpunit\\autoload.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Rule\\InvocationOrder.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\SelfDescribing.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Invocation.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Interface\\MockObject.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Interface\\Stub.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\InvocationMocker.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\InvocationStubber.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\MethodNameMatch.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\ParametersMatch.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\Stub.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\Identity.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Rule\\MethodName.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\InvocationHandler.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock-phpunit\\classes\\MockDisablerPHPUnit10.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Event\\Events\\Test\\Lifecycle\\FinishedSubscriber.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock-phpunit\\classes\\DefaultArgumentRemoverReturnTypes100.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock-phpunit\\classes\\MockObjectProxyReturnTypes100.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-backup\\src\\Helpers\\functions.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\helpers.php',", "is_string": true}, {"message": "'\\bootstrap\\app.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Container\\Container.php',", "is_string": true}, {"message": "'\\vendor\\psr\\container\\src\\ContainerInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Foundation\\Application.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Foundation\\CachesConfiguration.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Foundation\\CachesRoutes.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-kernel\\HttpKernelInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\EventServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\Context\\ContextServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RoutingServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Http\\Kernel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\InteractsWithTime.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Http\\Kernel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Container\\ContextualAttribute.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Tappable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Routing\\BindingRegistrar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Routing\\Registrar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ReflectsClosures.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Events\\Dispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollectionInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Request.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\Request.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\AcceptHeader.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\AcceptHeaderItem.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\FileBag.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\ParameterBag.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\HeaderBag.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\HeaderUtils.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\InputBag.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\ServerBag.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Concerns\\CanBePrecognitive.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Concerns\\InteractsWithContentTypes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Concerns\\InteractsWithFlashData.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Concerns\\InteractsWithInput.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Dumpable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\InteractsWithData.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Conditionable\\Traits\\Conditionable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Arrayable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Carbon.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Carbon.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Date.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Boundaries.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Comparison.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Converter.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\ToStringFormat.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Creator.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\ObjectInitialisation.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\LocalFactory.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Difference.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Macro.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Mixin.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\MagicParameter.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Modifiers.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Mutability.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Cast.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Options.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\StaticOptions.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Localization.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\StaticLocalization.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Rounding.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\IntervalRounding.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Serialization.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Test.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Timestamp.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Units.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Week.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonTimeZone.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonInterval.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\IntervalStep.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonConverterInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\FactoryImmutable.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\clock\\ClockInterface.php',", "is_string": true}, {"message": "'\\vendor\\psr\\clock\\src\\ClockInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonImmutable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadEnvironmentVariables.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Env.php',", "is_string": true}, {"message": "'\\vendor\\phpoption\\phpoption\\src\\PhpOption\\Option.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\RepositoryBuilder.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\ServerConstAdapter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\AdapterInterface.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\ReaderInterface.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\WriterInterface.php',", "is_string": true}, {"message": "'\\vendor\\phpoption\\phpoption\\src\\PhpOption\\Some.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\EnvConstAdapter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\PutenvAdapter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\MultiReader.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\MultiWriter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\ImmutableWriter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\AdapterRepository.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\RepositoryInterface.php',", "is_string": true}, {"message": "'\\vendor\\phpoption\\phpoption\\src\\PhpOption\\None.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php',", "is_string": true}, {"message": "'\\bootstrap\\cache\\config.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Config\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Config\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\EnvironmentDetector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\AliasLoader.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\CanBeEscapedWhenCastToString.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Enumerable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Jsonable.php',", "is_string": true}, {"message": "'\\bootstrap\\cache\\packages.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php',", "is_string": true}, {"message": "'\\bootstrap\\cache\\services.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\CookieServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasEvents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasGlobalScopes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasRelationships.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasTimestamps.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasUniqueIds.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HidesAttributes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\PreventsCircularRecursion.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\HasCollection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Broadcasting\\HasBroadcastChannel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Queue\\QueueableEntity.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Routing\\UrlRoutable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\SerializableClosure.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Signers\\Hmac.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Contracts\\Signer.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Serializers\\Signed.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Contracts\\Serializable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FoundationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\AggregateServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Testing\\ParallelTestingServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Testing\\Concerns\\TestDatabases.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\DeferrableProvider.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\AbstractCloner.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\ClonerInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\HtmlDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Dumper\\HtmlDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Dumper\\CliDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Dumper\\AbstractDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Dumper\\DataDumperInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\DumperInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Concerns\\ResolvesDumpSource.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\VarCloner.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\HigherOrderTapProxy.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Caster\\ReflectionCaster.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\VarDumper.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Uri.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Htmlable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Responsable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Reflector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\PaginationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\PaginationState.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\Paginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Pagination\\Paginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\CursorPaginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractCursorPaginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Pagination\\CursorPaginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\arietimmerman\\laravel-scim-server\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-dompdf\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\eduardokum\\laravel-mail-auto-embed\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\intervention\\image\\src\\Intervention\\Image\\ImageServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\intervention\\image\\src\\Intervention\\Image\\ImageServiceProviderLaravelRecent.php',", "is_string": true}, {"message": "'\\vendor\\laravel-notification-channels\\google-chat\\src\\GoogleChatServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel-notification-channels\\microsoft-teams\\src\\MicrosoftTeamsServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Notification.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Manager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Notifications\\Dispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Notifications\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\Passport.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\ContextualBindingBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Container\\ContextualBindingBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Auth.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\slack-notification-channel\\src\\SlackChannelServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\ui\\src\\UiServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\LivewireServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\LivewireManager.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\EventBus.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\PersistentMiddleware\\PersistentMiddleware.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\Mechanism.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\FrontendAssets\\FrontendAssets.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendBlade.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\CompileLivewireTags\\CompileLivewireTags.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\RenderComponent.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\DataStore.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Laravel\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\collision\\src\\Adapters\\Laravel\\CollisionServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\phpinsights\\src\\Application\\Adapters\\Laravel\\InsightsServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\phpinsights\\src\\Application\\Injectors\\Repositories.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\termwind\\src\\Laravel\\TermwindServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\osa-eg\\laravel-teams-notification\\src\\LaravelTeamsNotificationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\pragmarx\\google2fa-laravel\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-backup\\src\\BackupServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessAssets.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessBladeComponents.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessCommands.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessConfigs.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessInertia.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessMigrations.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessRoutes.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessServiceProviders.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessTranslations.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessViewComposers.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessViews.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessViewSharedData.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Package.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasAssets.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasBladeComponents.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasCommands.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasConfigs.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasInertia.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasInstallCommand.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasMigrations.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasRoutes.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasServiceProviders.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasTranslations.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasViewComposers.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasViews.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasViewSharedData.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\IgnitionServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\ignition\\src\\Config\\IgnitionConfig.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\ignition\\src\\Config\\FileConfigManager.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\ignition\\src\\Contracts\\ConfigManager.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\error-solutions\\src\\SolutionProviderRepository.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\error-solutions\\src\\Contracts\\SolutionProviderRepository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Log.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\ParsesLogConfiguration.php',", "is_string": true}, {"message": "'\\vendor\\psr\\log\\src\\LoggerInterface.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Level.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-signal-aware-command\\src\\SignalAwareCommandServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\BreadcrumbsServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Route.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CreatesRegularExpressionRouteConstraints.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\FiltersControllerMiddleware.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php',", "is_string": true}, {"message": "'\\vendor\\unicodeveloper\\laravel-password\\src\\DumbPasswordServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\AppServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\AuthServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\AuthServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\EventServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\RouteServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\SettingsServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\ValidationServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\BladeServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\LivewireServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\MacroServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\SamlServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\BreadcrumbsServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\ConnectionResolverInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Renderer\\Listener.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteBinding.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\debugbar-routes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteAction.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteUri.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteGroup.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\console\\Application.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\service-contracts\\ResetInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Console\\Application.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Event.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\routes\\web.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\ui\\src\\AuthRouteMethods.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\AboutCommand.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\console\\Command\\Command.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\console\\Command\\SignalableCommandInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\ConfiguresPrompts.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\HasParameters.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\InteractsWithIO.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\InteractsWithSignals.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\PromptsForMissingInput.php',", "is_string": true}, {"message": "'\\vendor\\composer\\InstalledVersions.php',", "is_string": true}, {"message": "'\\vendor\\composer\\installed.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\console\\Output\\OutputInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Stringable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Blade.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Compiler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesAuthorizations.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesClasses.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesComments.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesComponents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesConditionals.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesEchos.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesErrors.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesFragments.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesHelpers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesIncludes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesInjections.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesJson.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesJs.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesLayouts.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesLoops.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesRawPhp.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesSessions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesStacks.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesStyles.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesTranslations.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesUseStatements.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\CompilerInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\EngineResolver.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\CompileLivewireTags\\LivewireTagPrecompiler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\ComponentHookRegistry.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportWireModelingNestedComponents\\SupportWireModelingNestedComponents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\ComponentHook.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportMultipleRootElementDetection\\SupportMultipleRootElementDetection.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\SupportDisablingBackButtonCache.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportNestedComponentListeners\\SupportNestedComponentListeners.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportMorphAwareIfStatement\\SupportMorphAwareIfStatement.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Livewire.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportAutoInjectedAssets\\SupportAutoInjectedAssets.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportComputed\\SupportLegacyComputedPropertySyntax.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportNestingComponents\\SupportNestingComponents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportScriptsAndAssets\\SupportScriptsAndAssets.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportBladeAttributes\\SupportBladeAttributes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ComponentAttributeBag.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportConsoleCommands\\SupportConsoleCommands.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\View\\View.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Renderable.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportReactiveProps\\SupportReactiveProps.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportFileDownloads\\SupportFileDownloads.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportJsEvaluation\\SupportJsEvaluation.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportQueryString\\SupportQueryString.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportFileUploads\\SupportFileUploads.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportTeleporting\\SupportTeleporting.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportLazyLoading\\SupportLazyLoading.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportFormObjects\\SupportFormObjects.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportAttributes\\SupportAttributes.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination\\SupportPagination.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportValidation\\SupportValidation.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportIsolating\\SupportIsolating.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportRedirects\\SupportRedirects.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportStreaming\\SupportStreaming.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportNavigate\\SupportNavigate.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Vite.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportEntangle\\SupportEntangle.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportLocales\\SupportLocales.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportTesting\\SupportTesting.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\SupportModels.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportEvents\\SupportEvents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportLegacyModels\\SupportLegacyModels.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportWireables\\SupportWireables.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Translator.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation-contracts\\TranslatorInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Translator.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\MessageCatalogue.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\MessageCatalogueInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\MetadataAwareInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\CatalogueMetadataAwareInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\TranslatorBagInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation-contracts\\LocaleAwareInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\lazy\\Carbon\\TranslatorStrongType.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\AbstractTranslator.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\TranslatorStrongTypeInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Loader\\ArrayLoader.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Loader\\LoaderInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\MessageFormatter\\MessageFormatterMapper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Formatter\\MessageFormatterInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\lazy\\Carbon\\MessageFormatter\\MessageFormatterMapperStrongType.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Formatter\\MessageFormatter.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Formatter\\IntlFormatter.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Formatter\\IntlFormatterInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\IdentityTranslator.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation-contracts\\TranslatorTrait.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Lang\\en.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Lang\\en_US.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonPeriod.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\lazy\\Carbon\\UnprotectedDatePeriod.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Date.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\DateFactory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Events\\LocaleUpdated.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-backup\\src\\Notifications\\EventHandler.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-backup\\src\\Listeners\\EncryptBackupArchive.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\ignition-routes.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Http\\Controllers\\HealthCheckController.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Http\\Controllers\\ExecuteSolutionController.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Validation\\ValidatesRequests.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Http\\Controllers\\UpdateConfigController.php',", "is_string": true}, {"message": "'\\app\\Exceptions\\Handler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Debug\\ExceptionHandler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\ReportableHandler.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\DumpRecorder\\DumpRecorder.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\DumpRecorder\\MultiDumpHandler.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\JobRecorder\\JobRecorder.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\LogRecorder\\LogRecorder.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\QueryRecorder\\QueryRecorder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\QueueServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Serializers\\Native.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\QueueManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Queue\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Queue\\Monitor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Validator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Validation\\Factory.php',", "is_string": true}, {"message": "'\\app\\Providers\\SnipeTranslationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Translation\\TranslationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Translation\\FileLoader.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Translation\\Loader.php',", "is_string": true}, {"message": "'\\app\\Services\\SnipeTranslator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Translation\\Translator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\NamespacedItemResolver.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Translation\\Translator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifierInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\PresenceVerifierInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Routing\\UrlGenerator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Optional.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\NullHandler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\Handler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\HandlerInterface.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Logger.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\ResettableInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\Logger.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\LogRecord.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\JsonSerializableDateTimeImmutable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\Context\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\Events\\MessageLogged.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\LogRecorder\\LogMessage.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\URL.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Schema.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ConfigurationUrlParser.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DetectsConcurrencyErrors.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DetectsLostConnections.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\ConnectionInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Grammars\\MySqlGrammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Grammars\\Grammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Grammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\CompilesJsonPaths.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseTransactionsManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Events\\ConnectionEstablished.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Events\\ConnectionEvent.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Grammars\\Grammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php',", "is_string": true}, {"message": "'\\app\\Models\\Asset.php',", "is_string": true}, {"message": "'\\app\\Models\\Depreciable.php',", "is_string": true}, {"message": "'\\app\\Models\\SnipeModel.php',", "is_string": true}, {"message": "'\\app\\Models\\CompanyableTrait.php',", "is_string": true}, {"message": "'\\app\\Models\\Traits\\HasUploads.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Factories\\HasFactory.php',", "is_string": true}, {"message": "'\\app\\Models\\Loggable.php',", "is_string": true}, {"message": "'\\app\\Models\\Requestable.php',", "is_string": true}, {"message": "'\\app\\Presenters\\Presentable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\SoftDeletes.php',", "is_string": true}, {"message": "'\\vendor\\watson\\validating\\src\\ValidatingTrait.php',", "is_string": true}, {"message": "'\\vendor\\watson\\validating\\src\\Injectors\\UniqueInjector.php',", "is_string": true}, {"message": "'\\app\\Http\\Traits\\UniqueUndeletedTrait.php',", "is_string": true}, {"message": "'\\app\\Models\\Traits\\Acceptable.php',", "is_string": true}, {"message": "'\\app\\Models\\Traits\\Searchable.php',", "is_string": true}, {"message": "'\\app\\Models\\CompanyableScope.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Scope.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\SoftDeletingScope.php',", "is_string": true}, {"message": "'\\vendor\\watson\\validating\\src\\ValidatingObserver.php',", "is_string": true}, {"message": "'\\app\\Observers\\AssetObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\User.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Authenticatable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Auth\\Access\\Authorizable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Passwords\\CanResetPassword.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\HasApiTokens.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Notifiable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\HasDatabaseNotifications.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\RoutesNotifications.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Authenticatable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Access\\Authorizable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\CanResetPassword.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Translation\\HasLocalePreference.php',", "is_string": true}, {"message": "'\\app\\Observers\\UserObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\Accessory.php',", "is_string": true}, {"message": "'\\app\\Observers\\AccessoryObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\Component.php',", "is_string": true}, {"message": "'\\app\\Observers\\ComponentObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\Consumable.php',", "is_string": true}, {"message": "'\\app\\Observers\\ConsumableObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\License.php',", "is_string": true}, {"message": "'\\app\\Observers\\LicenseObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\Setting.php',", "is_string": true}, {"message": "'\\app\\Observers\\SettingObserver.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Gate.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\HandlesAuthorization.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Access\\Gate.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Unit.php',", "is_string": true}, {"message": "'\\app\\Listeners\\LogListener.php',", "is_string": true}, {"message": "'\\app\\Listeners\\CheckoutableListener.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\RateLimiter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cache\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\FileStore.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RetrievesMultipleKeys.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cache\\Store.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cache\\LockProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cache\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\psr\\simple-cache\\src\\CacheInterface.php',", "is_string": true}, {"message": "'\\routes\\api.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResourceRegistrar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\PendingResourceRegistration.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Pluralizer.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\InflectorFactory.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Language.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\English\\InflectorFactory.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\GenericLanguageInflectorFactory.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\LanguageInflectorFactory.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\English\\Rules.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Ruleset.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Transformations.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\WordInflector.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\English\\Inflectible.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Transformation.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Pattern.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Patterns.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\English\\Uninflected.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Substitutions.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Substitution.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Word.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Inflector.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\CachedWordInflector.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\RulesetInflector.php',", "is_string": true}, {"message": "'\\routes\\web\\hardware.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Support\\ClosureScope.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Support\\ReflectionClosure.php',", "is_string": true}, {"message": "'\\routes\\web\\models.php',", "is_string": true}, {"message": "'\\routes\\web\\accessories.php',", "is_string": true}, {"message": "'\\routes\\web\\licenses.php',", "is_string": true}, {"message": "'\\routes\\web\\locations.php',", "is_string": true}, {"message": "'\\routes\\web\\consumables.php',", "is_string": true}, {"message": "'\\routes\\web\\fields.php',", "is_string": true}, {"message": "'\\routes\\web\\components.php',", "is_string": true}, {"message": "'\\routes\\web\\users.php',", "is_string": true}, {"message": "'\\routes\\web\\kits.php',", "is_string": true}, {"message": "'\\routes\\web.php',", "is_string": true}, {"message": "'\\app\\Livewire\\Importer.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Component.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Auth\\Access\\AuthorizesRequests.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Concerns\\InteractsWithProperties.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportEvents\\HandlesEvents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportRedirects\\HandlesRedirects.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportStreaming\\HandlesStreaming.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportAttributes\\HandlesAttributes.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportValidation\\HandlesValidation.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportFormObjects\\HandlesFormObjects.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportJsEvaluation\\HandlesJsEvaluation.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\HandlesDisablingBackButtonCache.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\HealthController.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\DashboardController.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\Controller.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bus\\DispatchesJobs.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\GroupsController.php',", "is_string": true}, {"message": "'\\routes\\scim.php',", "is_string": true}, {"message": "'\\vendor\\arietimmerman\\laravel-scim-server\\src\\RouteProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewFinderInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesFragments.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesLayouts.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesLoops.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesStacks.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesTranslations.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\View\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewName.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\App.php',", "is_string": true}, {"message": "'\\resources\\macros\\macros.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\FormFacade.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\HtmlServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\FormBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\Componentable.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\HtmlBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\FileSessionHandler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Session\\Session.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Breadcrumbs.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Manager.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Trail.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Registrar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Pipeline\\Pipeline.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\TrustProxies.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\NoSessionStore.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\Concerns\\ExcludesPaths.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\MaintenanceModeManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\FileBasedMaintenanceMode.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Foundation\\MaintenanceMode.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SymfonySessionDecorator.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\Session\\SessionInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ViewErrorBag.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckForSetup.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsWhereDateClauses.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ExplainsQueries.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Database\\Query\\Builder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Database\\Eloquent\\Builder.php',", "is_string": true}, {"message": "'\\app\\Models\\Company.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectorInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Events\\StatementPrepared.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Events\\QueryExecuted.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\QueryRecorder\\Query.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Collection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\InteractsWithDictionary.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Queue\\QueueableCollection.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckForDebug.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\CreatesUserProviders.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\StatefulGuard.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Guard.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\SupportsBasicAuth.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\UserProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Hashing\\HashServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Hashing\\HashManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Hashing\\Hasher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Timebox.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\CookieJar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cookie\\QueueingFactory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cookie\\Factory.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\TrimStrings.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\SecurityHeaders.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\PreventBackHistory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php',", "is_string": true}, {"message": "'\\vendor\\fruitcake\\php-cors\\src\\CorsService.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\IpUtils.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\LaravelDebugbar.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DebugBar.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\Support\\RequestIdGenerator.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\RequestIdGeneratorInterface.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\Storage\\FilesystemStorage.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\Storage\\StorageInterface.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\PhpInfoCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\DataCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\HasDataFormatter.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\HasXdebugLinks.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\DataCollectorInterface.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\Renderable.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\MessagesCollector.php',", "is_string": true}, {"message": "'\\vendor\\psr\\log\\src\\AbstractLogger.php',", "is_string": true}, {"message": "'\\vendor\\psr\\log\\src\\LoggerTrait.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\MessagesAggregateInterface.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\AssetProvider.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\TimeDataCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\DataFormatter.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\DataFormatterInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\error-handler\\ErrorRenderer\\FileLinkFormatter.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\error-handler\\ErrorRenderer\\ErrorRendererInterface.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\MemoryCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\ExceptionsCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\LaravelCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\EventCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataFormatter\\SimpleFormatter.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\ViewCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\RouteCollector.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\StreamHandler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\AbstractProcessingHandler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\AbstractHandler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\ProcessableHandlerTrait.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\FormattableHandlerTrait.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\ProcessableHandlerInterface.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\FormattableHandlerInterface.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Utils.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Formatter\\LineFormatter.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Formatter\\NormalizerFormatter.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Formatter\\FormatterInterface.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\QueryCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\PDO\\PDOCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataFormatter\\QueryFormatter.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\ObjectCountCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\LivewireCollector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\MailServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\Bridge\\Symfony\\SymfonyMailCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\LogsCollector.php',", "is_string": true}, {"message": "'\\vendor\\psr\\log\\src\\LogLevel.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\FilesCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\MultiAuthCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\GateCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\JavascriptRenderer.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\JavascriptRenderer.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Events\\Routing.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\routing\\Route.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\routing\\RouteCompiler.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\routing\\RouteCompilerInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\routing\\CompiledRoute.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\UriValidator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\ValidatorInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\MethodValidator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\SchemeValidator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\HostValidator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteParameterBinder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Events\\RouteMatched.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\Auth\\LoginController.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\ui\\auth-backend\\ThrottlesLogins.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Contracts\\ControllerDispatcher.php',", "is_string": true}, {"message": "'\\app\\Services\\Saml.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Session.php',", "is_string": true}, {"message": "'\\vendor\\onelogin\\php-saml\\src\\Saml2\\Auth.php',", "is_string": true}, {"message": "'\\vendor\\onelogin\\php-saml\\src\\Saml2\\Settings.php',", "is_string": true}, {"message": "'\\vendor\\onelogin\\php-saml\\src\\Saml2\\Error.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerMiddlewareOptions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\MiddlewareNameResolver.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\SortedMiddleware.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\EncryptCookies.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\VerifyCsrfToken.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckLocale.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckUserIsActivated.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckForTwoFactor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\AssetCountForSidebar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Session\\Middleware\\AuthenticatesSessions.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\RedirectIfAuthenticated.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\Encrypter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Encryption\\Encrypter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Encryption\\StringEncrypter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Encryption\\DecryptException.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\CookieValuePrefix.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php',", "is_string": true}, {"message": "'\\app\\Helpers\\Helper.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\ApiTokenCookieFactory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php',", "is_string": true}, {"message": "'\\app\\Models\\Statuslabel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\BelongsTo.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\ComparesRelatedModels.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\SupportsDefaultModels.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteSignatureParameters.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\View\\Engine.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Events\\PreparingResponse.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\Response.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\ResponseHeaderBag.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\ResponseTrait.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\DeterministicBladeKeys.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Drawer\\Regexes.php',", "is_string": true}, {"message": "'\\storage\\framework\\views\\e4d81228f444002bb7b053c5fc9f59ad.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\auth\\general.php',", "is_string": true}, {"message": "'\\storage\\framework\\views\\3b1b16c575e20bc87c056cbfccd78ad8.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\MessageBag.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\MessageBag.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\MessageProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\AnonymousComponent.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ComponentSlot.php',", "is_string": true}, {"message": "'\\storage\\framework\\views\\e42f7a353f1c45b64eeb8c8510b72bd0.php',", "is_string": true}, {"message": "'\\app\\Helpers\\IconHelper.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\admin\\users\\table.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteUrlGenerator.php',", "is_string": true}, {"message": "'\\storage\\framework\\views\\63c84dcb0986168c6cc62bf14d4b65f5.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Mix.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\HtmlString.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Events\\ResponsePrepared.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\SymfonyHttpDriver.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\HttpDriverInterface.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\SessionCollector.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\uid\\Ulid.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\uid\\AbstractUid.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\uid\\HashableInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\uid\\TimeBasedUidInterface.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\RequestCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\Support\\Clockwork\\ClockworkCollector.php',", "is_string": true}], "count": 950}, "auth": {"guards": {"web": "null", "api": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000\"\n]", "PHPDEBUGBAR_STACK_DATA": "array:1 [\n  \"01K2S0HDEXBJCP50R25JPV52EW\" => null\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_token": "XcFb6uBj6sHVSCD8PzSZfpkew7wBekXM9V2bT1eX", "backUrl": "http://127.0.0.1:8000/setup/user?"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/login", "action_name": "login", "controller_action": "App\\Http\\Controllers\\Auth\\LoginController@showLoginForm", "uri": "GET login", "controller": "App\\Http\\Controllers\\Auth\\LoginController@showLoginForm<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FAuth%2FLoginController.php&line=63\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FAuth%2FLoginController.php&line=63\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Auth/LoginController.php:63-89</a>", "middleware": "web, web", "duration": "3.95s", "peak_memory": "40MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1093217846 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1093217846\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2004159812 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2004159812\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-19563331 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Microsoft Edge&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/setup/user?</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"996 characters\">csrftoken=eRZeQ4hlaHT8dltJRqLktclcixjXegkX; snipeit_passport_token=eyJpdiI6IlVzZWVFNUppb2NTU0tvNWRkVFBLRXc9PSIsInZhbHVlIjoiVXZaaXhWbk1uSmt6QmMvVGxidFRWSDBiT1RCdDBtNjZqc0FscStBZGYvaWViZmlxQXUrdkNyaU00OUpMYk5lSWo4V0I5bldUMVNmQnl2K0xaeUU5alFqUVVNMDhFUFZ0RHEyN0pZK0dxRmdObGxEMXV0SFp6dzg0MlMvajhEQVRrcnMrMUFxQjJCZ3pid0tYWGNqSGZMNnhSMHBxU3l0bDYySnhQZXBORE5zN3FWMDQycGlPbFIyTnFydjQ3cURQczhUN2NvV0FWNHQ1Y0I5UXRwM3dlY05HVlRoYnhWQ2VFR1h0YXNGaG1GVEVxRTZSaDQ1MkhYaEFxSWhPQkFQelgwcnNGZlNURGxZbEl3c3VobUNqWWVsZmloclBUNTFoUExCN0tyQWR2VzQ2dGVZazBkbnY5Qk5EQ3hFeFlFb1oiLCJtYWMiOiI4OWRjNTE1ODAzZmUwZmEyYjI1MzVhZTAyZTdlNGE2OTY2Yzk5YjQ3MDIzZjFjNWM1MjdhNWIyYWU5YjBkOTk3IiwidGFnIjoiIn0%3D; snipeit_session=vBVKm9lPdnne5sm3qrtXfsPa6iXzkQXFeWoMIQPS; XSRF-TOKEN=eyJpdiI6InFPVjRPZkhxSWtBTk1CWWlML1VKWWc9PSIsInZhbHVlIjoielZYOFBHYmFiODhVOTkwVllBZFFUbXVueUp4RFpWRm8vdTdLUGo3UEM4ajNraGFsMUthOWVkeWFNNjB3ZWlBNyIsIm1hYyI6ImNjZDUyZTVlZWFlNmMyY2I2ZmM2ZDY3ZWRjZDljMzYyNDk5YjA3YjI2OTg0Zjc4ZjNmZmE1NDIyY2I1ZmM5MjkiLCJ0YWciOiIifQ%3D%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-19563331\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>csrftoken</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>snipeit_passport_token</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>snipeit_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 16 Aug 2025 08:39:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-clockwork-id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">01K2S0HHC5QSDQ97XVHT6A4ZBR</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-clockwork-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-clockwork-path</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">_debugbar/clockwork/</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-909367803 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K2S0HDEXBJCP50R25JPV52EW</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XcFb6uBj6sHVSCD8PzSZfpkew7wBekXM9V2bT1eX</span>\"\n  \"<span class=sf-dump-key>backUrl</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/setup/user?</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-909367803\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/login", "action_name": "login", "controller_action": "App\\Http\\Controllers\\Auth\\LoginController@showLoginForm"}, "badge": null}, "clockwork": {"getData": [], "postData": [], "headers": {"host": ["127.0.0.1:8000"], "connection": ["keep-alive"], "cache-control": ["max-age=0"], "upgrade-insecure-requests": ["1"], "user-agent": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"], "accept": ["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"], "sec-fetch-site": ["same-origin"], "sec-fetch-mode": ["navigate"], "sec-fetch-user": ["?1"], "sec-fetch-dest": ["document"], "sec-ch-ua": ["\"Not;A=Brand\";v=\"99\", \"Microsoft Edge\";v=\"139\", \"Chromium\";v=\"139\""], "sec-ch-ua-mobile": ["?0"], "sec-ch-ua-platform": ["\"Windows\""], "referer": ["http://127.0.0.1:8000/setup/user?"], "accept-encoding": ["gzip, deflate, br, zstd"], "accept-language": ["en-US,en;q=0.9"], "cookie": ["csrftoken=eRZeQ4hlaHT8dltJRqLktclcixjXegkX; snipeit_passport_token=eyJpdiI6IlVzZWVFNUppb2NTU0tvNWRkVFBLRXc9PSIsInZhbHVlIjoiVXZaaXhWbk1uSmt6QmMvVGxidFRWSDBiT1RCdDBtNjZqc0FscStBZGYvaWViZmlxQXUrdkNyaU00OUpMYk5lSWo4V0I5bldUMVNmQnl2K0xaeUU5alFqUVVNMDhFUFZ0RHEyN0pZK0dxRmdObGxEMXV0SFp6dzg0MlMvajhEQVRrcnMrMUFxQjJCZ3pid0tYWGNqSGZMNnhSMHBxU3l0bDYySnhQZXBORE5zN3FWMDQycGlPbFIyTnFydjQ3cURQczhUN2NvV0FWNHQ1Y0I5UXRwM3dlY05HVlRoYnhWQ2VFR1h0YXNGaG1GVEVxRTZSaDQ1MkhYaEFxSWhPQkFQelgwcnNGZlNURGxZbEl3c3VobUNqWWVsZmloclBUNTFoUExCN0tyQWR2VzQ2dGVZazBkbnY5Qk5EQ3hFeFlFb1oiLCJtYWMiOiI4OWRjNTE1ODAzZmUwZmEyYjI1MzVhZTAyZTdlNGE2OTY2Yzk5YjQ3MDIzZjFjNWM1MjdhNWIyYWU5YjBkOTk3IiwidGFnIjoiIn0%3D; snipeit_session=vBVKm9lPdnne5sm3qrtXfsPa6iXzkQXFeWoMIQPS; XSRF-TOKEN=eyJpdiI6InFPVjRPZkhxSWtBTk1CWWlML1VKWWc9PSIsInZhbHVlIjoielZYOFBHYmFiODhVOTkwVllBZFFUbXVueUp4RFpWRm8vdTdLUGo3UEM4ajNraGFsMUthOWVkeWFNNjB3ZWlBNyIsIm1hYyI6ImNjZDUyZTVlZWFlNmMyY2I2ZmM2ZDY3ZWRjZDljMzYyNDk5YjA3YjI2OTg0Zjc4ZjNmZmE1NDIyY2I1ZmM5MjkiLCJ0YWciOiIifQ%3D%3D"]}, "cookies": {"csrftoken": null, "snipeit_passport_token": null, "snipeit_session": null, "XSRF-TOKEN": ""}, "uri": "/login", "method": "GET", "responseStatus": 200, "sessionData": {"url": {"intended": "http://127.0.0.1:8000"}, "PHPDEBUGBAR_STACK_DATA": {"01K2S0HDEXBJCP50R25JPV52EW": null}, "_previous": {"url": "http://127.0.0.1:8000"}, "_flash": {"old": [], "new": []}, "_token": "XcFb6uBj6sHVSCD8PzSZfpkew7wBekXM9V2bT1eX", "backUrl": "http://127.0.0.1:8000/setup/user?"}}}