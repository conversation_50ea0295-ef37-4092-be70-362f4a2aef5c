{"__meta": {"id": "01K2S0JYE2E8191N1GB7MJ2R19", "datetime": "2025-08-16 08:40:22", "utime": **********.212549, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[08:40:20] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 311", "message_html": null, "is_string": false, "label": "warning", "time": **********.080571, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 34, "start": **********.9829, "end": **********.212632, "duration": 32.229732036590576, "duration_str": "32.23s", "measures": [{"label": "Booting", "start": **********.9829, "relative_start": 0, "end": **********.943334, "relative_end": **********.943334, "duration": 0.****************, "duration_str": "960ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.943369, "relative_start": 0.****************, "end": **********.212641, "relative_end": 9.059906005859375e-06, "duration": 31.***************, "duration_str": "31.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.297129, "relative_start": 1.****************, "end": **********.325241, "relative_end": **********.325241, "duration": 0.028112173080444336, "duration_str": "28.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "select count(*) as aggregate from `assets` where `assets`.`deleted_at` is null", "start": **********.353043, "relative_start": 1.***************, "end": **********.354793, "relative_end": **********.354793, "duration": 0.0017499923706054688, "duration_str": "1.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets...", "start": **********.366166, "relative_start": 1.****************, "end": **********.368606, "relative_end": **********.368606, "duration": 0.0024399757385253906, "duration_str": "2.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`assigned_to` is null and exists (select *...", "start": **********.3753629, "relative_start": 1.392462968826294, "end": **********.377373, "relative_end": **********.377373, "duration": 0.0020101070404052734, "duration_str": "2.01ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assigned_to` > ? and `assets`.`deleted_at` is null", "start": **********.385474, "relative_start": 1.402574062347412, "end": **********.386834, "relative_end": **********.386834, "duration": 0.0013599395751953125, "duration_str": "1.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets...", "start": **********.395545, "relative_start": 1.4126451015472412, "end": **********.398045, "relative_end": **********.398045, "duration": 0.0025000572204589844, "duration_str": "2.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets...", "start": **********.403712, "relative_start": 1.4208121299743652, "end": **********.405142, "relative_end": **********.405142, "duration": 0.0014300346374511719, "duration_str": "1.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets...", "start": **********.4116979, "relative_start": 1.428797960281372, "end": **********.413338, "relative_end": **********.413338, "duration": 0.0016400814056396484, "duration_str": "1.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `byod` = ? and `assets`.`deleted_at` is null", "start": **********.418443, "relative_start": 1.4355430603027344, "end": **********.419473, "relative_end": **********.419473, "duration": 0.00102996826171875, "duration_str": "1.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`next_audit_date` is not null and `assets`...", "start": **********.426095, "relative_start": 1.443195104598999, "end": **********.427805, "relative_end": **********.427805, "duration": 0.0017099380493164062, "duration_str": "1.71ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`next_audit_date` is not null and `assets`...", "start": **********.434388, "relative_start": 1.4514880180358887, "end": **********.435698, "relative_end": **********.435698, "duration": 0.001310110092163086, "duration_str": "1.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`expected_checkin` is not null and `assets...", "start": **********.442418, "relative_start": 1.4595181941986084, "end": **********.444138, "relative_end": **********.444138, "duration": 0.0017199516296386719, "duration_str": "1.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`expected_checkin` is not null and `assets...", "start": **********.449603, "relative_start": 1.466703176498413, "end": **********.451173, "relative_end": **********.451173, "duration": 0.001569986343383789, "duration_str": "1.57ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`deleted_at` is null", "start": **********.47322, "relative_start": 1.4903202056884766, "end": **********.47472, "relative_end": **********.47472, "duration": 0.0014998912811279297, "duration_str": "1.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `accessories` where `accessories`.`deleted_at` is null", "start": **********.4804099, "relative_start": 1.4975099563598633, "end": **********.4836, "relative_end": **********.4836, "duration": 0.0031900405883789062, "duration_str": "3.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `license_seats` where `deleted_at` is null and `license_seats`.`de...", "start": **********.662049, "relative_start": 1.6791491508483887, "end": **********.665589, "relative_end": **********.665589, "duration": 0.0035400390625, "duration_str": "3.54ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `consumables` where `consumables`.`deleted_at` is null", "start": **********.672045, "relative_start": 1.6891450881958008, "end": **********.674375, "relative_end": **********.674375, "duration": 0.0023300647735595703, "duration_str": "2.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `components` where `components`.`deleted_at` is null", "start": **********.68059, "relative_start": 1.6976900100708008, "end": **********.68296, "relative_end": **********.68296, "duration": 0.002370119094848633, "duration_str": "2.37ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `users` where `users`.`deleted_at` is null", "start": **********.68879, "relative_start": 1.70589017868042, "end": **********.69014, "relative_end": **********.69014, "duration": 0.0013499259948730469, "duration_str": "1.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "Preparing Response", "start": **********.70009, "relative_start": 1.7171900272369385, "end": **********.206801, "relative_end": **********.206801, "duration": 30.50671100616455, "duration_str": "30.51s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets...", "start": **********.4675431, "relative_start": 14.48464322090149, "end": **********.469773, "relative_end": **********.469773, "duration": 0.002229928970336914, "duration_str": "2.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`expected_checkin` is not null and `assets...", "start": **********.4939392, "relative_start": 14.51103925704956, "end": **********.495959, "relative_end": **********.495959, "duration": 0.0020198822021484375, "duration_str": "2.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `next_audit_date` < ? and `next_audit_date` is not...", "start": **********.504244, "relative_start": 14.521344184875488, "end": **********.505784, "relative_end": **********.505784, "duration": 0.0015399456024169922, "duration_str": "1.54ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `asset_eol_date` is not null and `asset_eol_date` <...", "start": **********.5151942, "relative_start": 14.532294273376465, "end": **********.516894, "relative_end": **********.516894, "duration": 0.0016999244689941406, "duration_str": "1.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `warranty_months` is not null and DATE_ADD(purchase...", "start": **********.524127, "relative_start": 14.541227102279663, "end": **********.525197, "relative_end": **********.525197, "duration": 0.0010700225830078125, "duration_str": "1.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `consumables` where `qty` <= `min_amt` and `min_amt` > ? and `cons...", "start": **********.533109, "relative_start": 14.550209045410156, "end": **********.534949, "relative_end": **********.534949, "duration": 0.0018401145935058594, "duration_str": "1.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select `consumables`.*, (select count(*) from `consumables_users` where `consumables`.`id` = `consum...", "start": **********.980268, "relative_start": 29.997368097305298, "end": **********.982078, "relative_end": **********.982078, "duration": 0.0018100738525390625, "duration_str": "1.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select `accessories`.*, (select count(*) from `accessories_checkout` where `accessories`.`id` = `acc...", "start": **********.048371, "relative_start": 30.065471172332764, "end": **********.050291, "relative_end": **********.050291, "duration": 0.0019199848175048828, "duration_str": "1.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `components` where `min_amt` is not null and `components`.`deleted_at` is null", "start": **********.055886, "relative_start": 30.072986125946045, "end": **********.056826, "relative_end": **********.056826, "duration": 0.0009400844573974609, "duration_str": "940μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `models` where `min_amt` > ? and `models`.`deleted_at` is null", "start": **********.066099, "relative_start": 30.08319902420044, "end": **********.067649, "relative_end": **********.067649, "duration": 0.0015499591827392578, "duration_str": "1.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `licenses` where `min_amt` > ? and `licenses`.`deleted_at` is null", "start": **********.072809, "relative_start": 30.089909076690674, "end": **********.075229, "relative_end": **********.075229, "duration": 0.0024199485778808594, "duration_str": "2.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select `status_labels`.*, (select count(*) from `assets` where `status_labels`.`id` = `assets`.`stat...", "start": **********.92276, "relative_start": 31.939860105514526, "end": **********.92449, "relative_end": **********.92449, "duration": 0.0017299652099609375, "duration_str": "1.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.2.12", "Environment": "production", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "UTC", "Locale": "en-US"}}, "event": {"count": 322, "start": **********.9829, "end": **********.212913, "duration": 32.**************, "duration_str": "32.23s", "measures": [{"label": "Illuminate\\Routing\\Events\\Routing", "start": **********.297144, "relative_start": 1.***************, "end": **********.297144, "relative_end": **********.297144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\Routing"}, {"label": "Illuminate\\Routing\\Events\\RouteMatched", "start": **********.325288, "relative_start": 1.****************, "end": **********.325288, "relative_end": **********.325288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\RouteMatched"}, {"label": "Illuminate\\Foundation\\Events\\LocaleUpdated", "start": **********.350952, "relative_start": 1.****************, "end": **********.350952, "relative_end": **********.350952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Foundation\\Events\\LocaleUpdated"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.35384, "relative_start": 1.****************, "end": **********.35384, "relative_end": **********.35384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.360726, "relative_start": 1.**************, "end": **********.360726, "relative_end": **********.360726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "eloquent.booting: App\\Models\\Statuslabel", "start": **********.362205, "relative_start": 1.379305124282837, "end": **********.362205, "relative_end": **********.362205, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booting"}, {"label": "eloquent.booted: App\\Models\\Statuslabel", "start": **********.362518, "relative_start": 1.3796181678771973, "end": **********.362518, "relative_end": **********.362518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.367399, "relative_start": 1.3844990730285645, "end": **********.367399, "relative_end": **********.367399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.374086, "relative_start": 1.391185998916626, "end": **********.374086, "relative_end": **********.374086, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.375876, "relative_start": 1.3929760456085205, "end": **********.375876, "relative_end": **********.375876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.384544, "relative_start": 1.401643991470337, "end": **********.384544, "relative_end": **********.384544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.385991, "relative_start": 1.4030911922454834, "end": **********.385991, "relative_end": **********.385991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.393975, "relative_start": 1.4110751152038574, "end": **********.393975, "relative_end": **********.393975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.396964, "relative_start": 1.4140641689300537, "end": **********.396964, "relative_end": **********.396964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.402512, "relative_start": 1.419612169265747, "end": **********.402512, "relative_end": **********.402512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.404168, "relative_start": 1.4212679862976074, "end": **********.404168, "relative_end": **********.404168, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.410521, "relative_start": 1.4276211261749268, "end": **********.410521, "relative_end": **********.410521, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.412341, "relative_start": 1.429441213607788, "end": **********.412341, "relative_end": **********.412341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.417773, "relative_start": 1.434873104095459, "end": **********.417773, "relative_end": **********.417773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.418775, "relative_start": 1.4358751773834229, "end": **********.418775, "relative_end": **********.418775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.424557, "relative_start": 1.4416570663452148, "end": **********.424557, "relative_end": **********.424557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.426694, "relative_start": 1.4437940120697021, "end": **********.426694, "relative_end": **********.426694, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.433087, "relative_start": 1.4501872062683105, "end": **********.433087, "relative_end": **********.433087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.434842, "relative_start": 1.4519422054290771, "end": **********.434842, "relative_end": **********.434842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.440927, "relative_start": 1.4580271244049072, "end": **********.440927, "relative_end": **********.440927, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.442914, "relative_start": 1.4600141048431396, "end": **********.442914, "relative_end": **********.442914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.448204, "relative_start": 1.4653041362762451, "end": **********.448204, "relative_end": **********.448204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.450155, "relative_start": 1.4672551155090332, "end": **********.450155, "relative_end": **********.450155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.457291, "relative_start": 1.474390983581543, "end": **********.457291, "relative_end": **********.457291, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.473825, "relative_start": 1.4909250736236572, "end": **********.473825, "relative_end": **********.473825, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.47973, "relative_start": 1.4968299865722656, "end": **********.47973, "relative_end": **********.47973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.480832, "relative_start": 1.4979321956634521, "end": **********.480832, "relative_end": **********.480832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.488141, "relative_start": 1.5052411556243896, "end": **********.488141, "relative_end": **********.488141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "eloquent.booting: App\\Models\\LicenseSeat", "start": **********.58965, "relative_start": 1.6067500114440918, "end": **********.58965, "relative_end": **********.58965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booting"}, {"label": "eloquent.booted: App\\Models\\LicenseSeat", "start": **********.661042, "relative_start": 1.6781420707702637, "end": **********.661042, "relative_end": **********.661042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.662952, "relative_start": 1.6800520420074463, "end": **********.662952, "relative_end": **********.662952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.671271, "relative_start": 1.688371181488037, "end": **********.671271, "relative_end": **********.671271, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.672587, "relative_start": 1.6896870136260986, "end": **********.672587, "relative_end": **********.672587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.679724, "relative_start": 1.696824073791504, "end": **********.679724, "relative_end": **********.679724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.681089, "relative_start": 1.6981890201568604, "end": **********.681089, "relative_end": **********.681089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.688173, "relative_start": 1.705273151397705, "end": **********.688173, "relative_end": **********.688173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.689369, "relative_start": 1.7064690589904785, "end": **********.689369, "relative_end": **********.689369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.694199, "relative_start": 1.711299180984497, "end": **********.694199, "relative_end": **********.694199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "creating: dashboard", "start": **********.699494, "relative_start": 1.****************, "end": **********.699494, "relative_end": **********.699494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "Illuminate\\Routing\\Events\\PreparingResponse", "start": **********.700101, "relative_start": 1.****************, "end": **********.700101, "relative_end": **********.700101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\PreparingResponse"}, {"label": "composing: dashboard", "start": **********.705451, "relative_start": 1.****************, "end": **********.705451, "relative_end": **********.705451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.468442, "relative_start": 14.***************, "end": **********.468442, "relative_end": **********.468442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.476504, "relative_start": 14.***************, "end": **********.476504, "relative_end": **********.476504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.480583, "relative_start": 14.497683048248291, "end": **********.480583, "relative_end": **********.480583, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.480666, "relative_start": 14.497766017913818, "end": **********.480666, "relative_end": **********.480666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.483338, "relative_start": 14.500438213348389, "end": **********.483338, "relative_end": **********.483338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.483377, "relative_start": 14.500477075576782, "end": **********.483377, "relative_end": **********.483377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.484439, "relative_start": 14.5015389919281, "end": **********.484439, "relative_end": **********.484439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.484471, "relative_start": 14.50157117843628, "end": **********.484471, "relative_end": **********.484471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.485298, "relative_start": 14.502398014068604, "end": **********.485298, "relative_end": **********.485298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.485324, "relative_start": 14.502424001693726, "end": **********.485324, "relative_end": **********.485324, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.486165, "relative_start": 14.503265142440796, "end": **********.486165, "relative_end": **********.486165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.48619, "relative_start": 14.503290176391602, "end": **********.48619, "relative_end": **********.48619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.486974, "relative_start": 14.504074096679688, "end": **********.486974, "relative_end": **********.486974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.486999, "relative_start": 14.504099130630493, "end": **********.486999, "relative_end": **********.486999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.487802, "relative_start": 14.504902124404907, "end": **********.487802, "relative_end": **********.487802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.487826, "relative_start": 14.504926204681396, "end": **********.487826, "relative_end": **********.487826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.488577, "relative_start": 14.505676984786987, "end": **********.488577, "relative_end": **********.488577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.488601, "relative_start": 14.505701065063477, "end": **********.488601, "relative_end": **********.488601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.489376, "relative_start": 14.506476163864136, "end": **********.489376, "relative_end": **********.489376, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.4894, "relative_start": 14.506500005722046, "end": **********.4894, "relative_end": **********.4894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.490116, "relative_start": 14.507215976715088, "end": **********.490116, "relative_end": **********.490116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.490139, "relative_start": 14.50723910331726, "end": **********.490139, "relative_end": **********.490139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.490982, "relative_start": 14.508082151412964, "end": **********.490982, "relative_end": **********.490982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.491005, "relative_start": 14.508105039596558, "end": **********.491005, "relative_end": **********.491005, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.491714, "relative_start": 14.508814096450806, "end": **********.491714, "relative_end": **********.491714, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.491737, "relative_start": 14.5088369846344, "end": **********.491737, "relative_end": **********.491737, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.494856, "relative_start": 14.511956214904785, "end": **********.494856, "relative_end": **********.494856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.500655, "relative_start": 14.517755031585693, "end": **********.500655, "relative_end": **********.500655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.50093, "relative_start": 14.518030166625977, "end": **********.50093, "relative_end": **********.50093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.500968, "relative_start": 14.518068075180054, "end": **********.500968, "relative_end": **********.500968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.502204, "relative_start": 14.519304037094116, "end": **********.502204, "relative_end": **********.502204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.502238, "relative_start": 14.519338130950928, "end": **********.502238, "relative_end": **********.502238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.504798, "relative_start": 14.521898031234741, "end": **********.504798, "relative_end": **********.504798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.510824, "relative_start": 14.527924060821533, "end": **********.510824, "relative_end": **********.510824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.511103, "relative_start": 14.528203010559082, "end": **********.511103, "relative_end": **********.511103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.511141, "relative_start": 14.528241157531738, "end": **********.511141, "relative_end": **********.511141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.513344, "relative_start": 14.530444145202637, "end": **********.513344, "relative_end": **********.513344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.513387, "relative_start": 14.530487060546875, "end": **********.513387, "relative_end": **********.513387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.515792, "relative_start": 14.532891988754272, "end": **********.515792, "relative_end": **********.515792, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.521396, "relative_start": 14.538496017456055, "end": **********.521396, "relative_end": **********.521396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.521612, "relative_start": 14.53871202468872, "end": **********.521612, "relative_end": **********.521612, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.521645, "relative_start": 14.538745164871216, "end": **********.521645, "relative_end": **********.521645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.522663, "relative_start": 14.53976321220398, "end": **********.522663, "relative_end": **********.522663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.522696, "relative_start": 14.539796113967896, "end": **********.522696, "relative_end": **********.522696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.524477, "relative_start": 14.541577100753784, "end": **********.524477, "relative_end": **********.524477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.528623, "relative_start": 14.54572319984436, "end": **********.528623, "relative_end": **********.528623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.529288, "relative_start": 14.546388149261475, "end": **********.529288, "relative_end": **********.529288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.52933, "relative_start": 14.546430110931396, "end": **********.52933, "relative_end": **********.52933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.531215, "relative_start": 14.548315048217773, "end": **********.531215, "relative_end": **********.531215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.531255, "relative_start": 14.548355102539062, "end": **********.531255, "relative_end": **********.531255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.533797, "relative_start": 14.550897121429443, "end": **********.533797, "relative_end": **********.533797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.541453, "relative_start": 14.558552980422974, "end": **********.541453, "relative_end": **********.541453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.541736, "relative_start": 14.558835983276367, "end": **********.541736, "relative_end": **********.541736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.541779, "relative_start": 14.558879137039185, "end": **********.541779, "relative_end": **********.541779, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.543329, "relative_start": 14.560429096221924, "end": **********.543329, "relative_end": **********.543329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.543946, "relative_start": 14.561046123504639, "end": **********.543946, "relative_end": **********.543946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.609974, "relative_start": 14.627074003219604, "end": **********.609974, "relative_end": **********.609974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.682553, "relative_start": 14.699653148651123, "end": **********.682553, "relative_end": **********.682553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.683368, "relative_start": 14.700468063354492, "end": **********.683368, "relative_end": **********.683368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.684571, "relative_start": 14.701671123504639, "end": **********.684571, "relative_end": **********.684571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.685124, "relative_start": 14.702224016189575, "end": **********.685124, "relative_end": **********.685124, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.685664, "relative_start": 14.70276403427124, "end": **********.685664, "relative_end": **********.685664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: partials.bootstrap-table", "start": **********.686819, "relative_start": 14.703919172286987, "end": **********.686819, "relative_end": **********.686819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: partials.bootstrap-table", "start": **********.686886, "relative_start": 14.703986167907715, "end": **********.686886, "relative_end": **********.686886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.904755, "relative_start": 14.921855211257935, "end": **********.904755, "relative_end": **********.904755, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.904815, "relative_start": 14.921915054321289, "end": **********.904815, "relative_end": **********.904815, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.027213, "relative_start": 15.044313192367554, "end": 1755333605.027213, "relative_end": 1755333605.027213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.027271, "relative_start": 15.044371128082275, "end": 1755333605.027271, "relative_end": 1755333605.027271, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.029157, "relative_start": 15.046257019042969, "end": 1755333605.029157, "relative_end": 1755333605.029157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.029206, "relative_start": 15.046306133270264, "end": 1755333605.029206, "relative_end": 1755333605.029206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.03045, "relative_start": 15.047550201416016, "end": 1755333605.03045, "relative_end": 1755333605.03045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.030491, "relative_start": 15.047591209411621, "end": 1755333605.030491, "relative_end": 1755333605.030491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.031606, "relative_start": 15.0487060546875, "end": 1755333605.031606, "relative_end": 1755333605.031606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.03164, "relative_start": 15.048740148544312, "end": 1755333605.03164, "relative_end": 1755333605.03164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.032938, "relative_start": 15.05003809928894, "end": 1755333605.032938, "relative_end": 1755333605.032938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.032972, "relative_start": 15.050072193145752, "end": 1755333605.032972, "relative_end": 1755333605.032972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.033957, "relative_start": 15.05105710029602, "end": 1755333605.033957, "relative_end": 1755333605.033957, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.033987, "relative_start": 15.051087141036987, "end": 1755333605.033987, "relative_end": 1755333605.033987, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.034991, "relative_start": 15.052091121673584, "end": 1755333605.034991, "relative_end": 1755333605.034991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.03502, "relative_start": 15.052120208740234, "end": 1755333605.03502, "relative_end": 1755333605.03502, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.035964, "relative_start": 15.053064107894897, "end": 1755333605.035964, "relative_end": 1755333605.035964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.035991, "relative_start": 15.053091049194336, "end": 1755333605.035991, "relative_end": 1755333605.035991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.041568, "relative_start": 15.05866813659668, "end": 1755333605.041568, "relative_end": 1755333605.041568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.041605, "relative_start": 15.05870509147644, "end": 1755333605.041605, "relative_end": 1755333605.041605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.042741, "relative_start": 15.05984115600586, "end": 1755333605.042741, "relative_end": 1755333605.042741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.042775, "relative_start": 15.059875011444092, "end": 1755333605.042775, "relative_end": 1755333605.042775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.044012, "relative_start": 15.06111216545105, "end": 1755333605.044012, "relative_end": 1755333605.044012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.044048, "relative_start": 15.061148166656494, "end": 1755333605.044048, "relative_end": 1755333605.044048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.102087, "relative_start": 15.119187116622925, "end": 1755333605.102087, "relative_end": 1755333605.102087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.102166, "relative_start": 15.119266033172607, "end": 1755333605.102166, "relative_end": 1755333605.102166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.104214, "relative_start": 15.12131404876709, "end": 1755333605.104214, "relative_end": 1755333605.104214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.104273, "relative_start": 15.121373176574707, "end": 1755333605.104273, "relative_end": 1755333605.104273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.105608, "relative_start": 15.122708082199097, "end": 1755333605.105608, "relative_end": 1755333605.105608, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.10565, "relative_start": 15.122750043869019, "end": 1755333605.10565, "relative_end": 1755333605.10565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.107031, "relative_start": 15.124131202697754, "end": 1755333605.107031, "relative_end": 1755333605.107031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.107064, "relative_start": 15.12416410446167, "end": 1755333605.107064, "relative_end": 1755333605.107064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.108009, "relative_start": 15.125109195709229, "end": 1755333605.108009, "relative_end": 1755333605.108009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.108034, "relative_start": 15.125133991241455, "end": 1755333605.108034, "relative_end": 1755333605.108034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.108832, "relative_start": 15.125931978225708, "end": 1755333605.108832, "relative_end": 1755333605.108832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.108857, "relative_start": 15.125957012176514, "end": 1755333605.108857, "relative_end": 1755333605.108857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.110204, "relative_start": 15.127304077148438, "end": 1755333605.110204, "relative_end": 1755333605.110204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.110237, "relative_start": 15.127336978912354, "end": 1755333605.110237, "relative_end": 1755333605.110237, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.111497, "relative_start": 15.128597021102905, "end": 1755333605.111497, "relative_end": 1755333605.111497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.111525, "relative_start": 15.12862515449524, "end": 1755333605.111525, "relative_end": 1755333605.111525, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.112381, "relative_start": 15.129481077194214, "end": 1755333605.112381, "relative_end": 1755333605.112381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": 1755333605.112409, "relative_start": 15.129509210586548, "end": 1755333605.112409, "relative_end": 1755333605.112409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: layouts.default", "start": 1755333605.11464, "relative_start": 15.131740093231201, "end": 1755333605.11464, "relative_end": 1755333605.11464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: layouts.default", "start": 1755333605.114702, "relative_start": 15.131802082061768, "end": 1755333605.114702, "relative_end": 1755333605.114702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.857876, "relative_start": 29.87497615814209, "end": **********.857876, "relative_end": **********.857876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.940903, "relative_start": 29.958003044128418, "end": **********.940903, "relative_end": **********.940903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.940962, "relative_start": 29.958062171936035, "end": **********.940962, "relative_end": **********.940962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.943336, "relative_start": 29.96043610572815, "end": **********.943336, "relative_end": **********.943336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.943818, "relative_start": 29.960918188095093, "end": **********.943818, "relative_end": **********.943818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.943855, "relative_start": 29.960955142974854, "end": **********.943855, "relative_end": **********.943855, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.945825, "relative_start": 29.96292519569397, "end": **********.945825, "relative_end": **********.945825, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.946279, "relative_start": 29.96337914466858, "end": **********.946279, "relative_end": **********.946279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.946315, "relative_start": 29.963415145874023, "end": **********.946315, "relative_end": **********.946315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.948104, "relative_start": 29.965204000473022, "end": **********.948104, "relative_end": **********.948104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.948445, "relative_start": 29.965545177459717, "end": **********.948445, "relative_end": **********.948445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.948479, "relative_start": 29.96557903289795, "end": **********.948479, "relative_end": **********.948479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.949868, "relative_start": 29.966968059539795, "end": **********.949868, "relative_end": **********.949868, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.950201, "relative_start": 29.9673011302948, "end": **********.950201, "relative_end": **********.950201, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.950227, "relative_start": 29.967327117919922, "end": **********.950227, "relative_end": **********.950227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.951989, "relative_start": 29.969089031219482, "end": **********.951989, "relative_end": **********.951989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.952285, "relative_start": 29.969385147094727, "end": **********.952285, "relative_end": **********.952285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.952314, "relative_start": 29.969413995742798, "end": **********.952314, "relative_end": **********.952314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.954727, "relative_start": 29.971827030181885, "end": **********.954727, "relative_end": **********.954727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.955223, "relative_start": 29.972323179244995, "end": **********.955223, "relative_end": **********.955223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.955514, "relative_start": 29.9726140499115, "end": **********.955514, "relative_end": **********.955514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.955581, "relative_start": 29.972681045532227, "end": **********.955581, "relative_end": **********.955581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.95698, "relative_start": 29.974080085754395, "end": **********.95698, "relative_end": **********.95698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.957222, "relative_start": 29.974322080612183, "end": **********.957222, "relative_end": **********.957222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.957249, "relative_start": 29.97434902191162, "end": **********.957249, "relative_end": **********.957249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.958667, "relative_start": 29.975767135620117, "end": **********.958667, "relative_end": **********.958667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.958906, "relative_start": 29.976006031036377, "end": **********.958906, "relative_end": **********.958906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.958934, "relative_start": 29.97603416442871, "end": **********.958934, "relative_end": **********.958934, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.960193, "relative_start": 29.977293014526367, "end": **********.960193, "relative_end": **********.960193, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.960387, "relative_start": 29.977487087249756, "end": **********.960387, "relative_end": **********.960387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.960408, "relative_start": 29.977508068084717, "end": **********.960408, "relative_end": **********.960408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.96263, "relative_start": 29.979730129241943, "end": **********.96263, "relative_end": **********.96263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.963033, "relative_start": 29.980133056640625, "end": **********.963033, "relative_end": **********.963033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.963067, "relative_start": 29.980167150497437, "end": **********.963067, "relative_end": **********.963067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.964504, "relative_start": 29.98160409927368, "end": **********.964504, "relative_end": **********.964504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.964755, "relative_start": 29.981855154037476, "end": **********.964755, "relative_end": **********.964755, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.965107, "relative_start": 29.98220705986023, "end": **********.965107, "relative_end": **********.965107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.966517, "relative_start": 29.983617067337036, "end": **********.966517, "relative_end": **********.966517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "eloquent.booting: App\\Models\\ConsumableAssignment", "start": **********.970715, "relative_start": 29.987815141677856, "end": **********.970715, "relative_end": **********.970715, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booting"}, {"label": "eloquent.booted: App\\Models\\ConsumableAssignment", "start": **********.971037, "relative_start": 29.988137006759644, "end": **********.971037, "relative_end": **********.971037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.981053, "relative_start": 29.99815320968628, "end": **********.981053, "relative_end": **********.981053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.987486, "relative_start": 30.00458598136902, "end": **********.987486, "relative_end": **********.987486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "eloquent.booting: App\\Models\\AccessoryCheckout", "start": **********.046612, "relative_start": 30.063712120056152, "end": **********.046612, "relative_end": **********.046612, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booting"}, {"label": "eloquent.booted: App\\Models\\AccessoryCheckout", "start": **********.046883, "relative_start": 30.06398320198059, "end": **********.046883, "relative_end": **********.046883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.049132, "relative_start": 30.066232204437256, "end": **********.049132, "relative_end": **********.049132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.055059, "relative_start": 30.07215905189514, "end": **********.055059, "relative_end": **********.055059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.05622, "relative_start": 30.073320150375366, "end": **********.05622, "relative_end": **********.05622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.061274, "relative_start": 30.07837414741516, "end": **********.061274, "relative_end": **********.061274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "eloquent.booting: App\\Models\\AssetModel", "start": **********.065014, "relative_start": 30.08211398124695, "end": **********.065014, "relative_end": **********.065014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booting"}, {"label": "eloquent.booted: App\\Models\\AssetModel", "start": **********.065406, "relative_start": 30.08250617980957, "end": **********.065406, "relative_end": **********.065406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.066644, "relative_start": 30.083744049072266, "end": **********.066644, "relative_end": **********.066644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.072073, "relative_start": 30.089173078536987, "end": **********.072073, "relative_end": **********.072073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.073157, "relative_start": 30.090257167816162, "end": **********.073157, "relative_end": **********.073157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.079698, "relative_start": 30.096798181533813, "end": **********.079698, "relative_end": **********.079698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Log\\Events\\MessageLogged", "start": **********.081044, "relative_start": 30.098144054412842, "end": **********.081044, "relative_end": **********.081044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Log\\Events\\MessageLogged"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.081433, "relative_start": 30.098533153533936, "end": **********.081433, "relative_end": **********.081433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.081483, "relative_start": 30.098582983016968, "end": **********.081483, "relative_end": **********.081483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.083938, "relative_start": 30.101037979125977, "end": **********.083938, "relative_end": **********.083938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.904617, "relative_start": 31.921717166900635, "end": **********.904617, "relative_end": **********.904617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.904725, "relative_start": 31.921825170516968, "end": **********.904725, "relative_end": **********.904725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.907208, "relative_start": 31.92430806159973, "end": **********.907208, "relative_end": **********.907208, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.907551, "relative_start": 31.92465114593506, "end": **********.907551, "relative_end": **********.907551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.907587, "relative_start": 31.924687147140503, "end": **********.907587, "relative_end": **********.907587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.90887, "relative_start": 31.92597007751465, "end": **********.90887, "relative_end": **********.90887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.908908, "relative_start": 31.926007986068726, "end": **********.908908, "relative_end": **********.908908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.910329, "relative_start": 31.92742919921875, "end": **********.910329, "relative_end": **********.910329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.91049, "relative_start": 31.927590131759644, "end": **********.91049, "relative_end": **********.91049, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.910518, "relative_start": 31.9276180267334, "end": **********.910518, "relative_end": **********.910518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.911643, "relative_start": 31.92874312400818, "end": **********.911643, "relative_end": **********.911643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.911675, "relative_start": 31.92877507209778, "end": **********.911675, "relative_end": **********.911675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.913013, "relative_start": 31.930113077163696, "end": **********.913013, "relative_end": **********.913013, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.913156, "relative_start": 31.930256128311157, "end": **********.913156, "relative_end": **********.913156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.913183, "relative_start": 31.930283069610596, "end": **********.913183, "relative_end": **********.913183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.913913, "relative_start": 31.931013107299805, "end": **********.913913, "relative_end": **********.913913, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.91393, "relative_start": 31.93103003501892, "end": **********.91393, "relative_end": **********.91393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.915039, "relative_start": 31.9321391582489, "end": **********.915039, "relative_end": **********.915039, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.915163, "relative_start": 31.932263135910034, "end": **********.915163, "relative_end": **********.915163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.915186, "relative_start": 31.932286024093628, "end": **********.915186, "relative_end": **********.915186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.916035, "relative_start": 31.93313503265381, "end": **********.916035, "relative_end": **********.916035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.91606, "relative_start": 31.933160066604614, "end": **********.91606, "relative_end": **********.91606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.917213, "relative_start": 31.93431305885315, "end": **********.917213, "relative_end": **********.917213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.917336, "relative_start": 31.934436082839966, "end": **********.917336, "relative_end": **********.917336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.91736, "relative_start": 31.934460163116455, "end": **********.91736, "relative_end": **********.91736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.918563, "relative_start": 31.935662984848022, "end": **********.918563, "relative_end": **********.918563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.918816, "relative_start": 31.93591618537903, "end": **********.918816, "relative_end": **********.918816, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.918842, "relative_start": 31.93594217300415, "end": **********.918842, "relative_end": **********.918842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.919706, "relative_start": 31.936806201934814, "end": **********.919706, "relative_end": **********.919706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.919732, "relative_start": 31.936832189559937, "end": **********.919732, "relative_end": **********.919732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.920534, "relative_start": 31.937633991241455, "end": **********.920534, "relative_end": **********.920534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.920749, "relative_start": 31.937849044799805, "end": **********.920749, "relative_end": **********.920749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.923557, "relative_start": 31.940657138824463, "end": **********.923557, "relative_end": **********.923557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.931116, "relative_start": 31.948216199874878, "end": **********.931116, "relative_end": **********.931116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.93178, "relative_start": 31.948880195617676, "end": **********.93178, "relative_end": **********.93178, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.931865, "relative_start": 31.948965072631836, "end": **********.931865, "relative_end": **********.931865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.936175, "relative_start": 31.953275203704834, "end": **********.936175, "relative_end": **********.936175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.936249, "relative_start": 31.953349113464355, "end": **********.936249, "relative_end": **********.936249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.938393, "relative_start": 31.955493211746216, "end": **********.938393, "relative_end": **********.938393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.938445, "relative_start": 31.95554518699646, "end": **********.938445, "relative_end": **********.938445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.939832, "relative_start": 31.956932067871094, "end": **********.939832, "relative_end": **********.939832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.939872, "relative_start": 31.956972122192383, "end": **********.939872, "relative_end": **********.939872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.941187, "relative_start": 31.958287000656128, "end": **********.941187, "relative_end": **********.941187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.941224, "relative_start": 31.958324193954468, "end": **********.941224, "relative_end": **********.941224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.944036, "relative_start": 31.96113610267639, "end": **********.944036, "relative_end": **********.944036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.944117, "relative_start": 31.961217164993286, "end": **********.944117, "relative_end": **********.944117, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.945959, "relative_start": 31.963059186935425, "end": **********.945959, "relative_end": **********.945959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.94602, "relative_start": 31.963119983673096, "end": **********.94602, "relative_end": **********.94602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.948312, "relative_start": 31.965412139892578, "end": **********.948312, "relative_end": **********.948312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.948611, "relative_start": 31.96571111679077, "end": **********.948611, "relative_end": **********.948611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.948639, "relative_start": 31.965739011764526, "end": **********.948639, "relative_end": **********.948639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.950133, "relative_start": 31.967233180999756, "end": **********.950133, "relative_end": **********.950133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.950389, "relative_start": 31.967489004135132, "end": **********.950389, "relative_end": **********.950389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.950415, "relative_start": 31.967514991760254, "end": **********.950415, "relative_end": **********.950415, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.95175, "relative_start": 31.968850135803223, "end": **********.95175, "relative_end": **********.95175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.953861, "relative_start": 31.970961093902588, "end": **********.953861, "relative_end": **********.953861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.9546, "relative_start": 31.971700191497803, "end": **********.9546, "relative_end": **********.9546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.955436, "relative_start": 31.972536087036133, "end": **********.955436, "relative_end": **********.955436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.95609, "relative_start": 31.97319006919861, "end": **********.95609, "relative_end": **********.95609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.956611, "relative_start": 31.973711013793945, "end": **********.956611, "relative_end": **********.956611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.956757, "relative_start": 31.973857164382935, "end": **********.956757, "relative_end": **********.956757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.956784, "relative_start": 31.973884105682373, "end": **********.956784, "relative_end": **********.956784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.958933, "relative_start": 31.976033210754395, "end": **********.958933, "relative_end": **********.958933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.959527, "relative_start": 31.976627111434937, "end": **********.959527, "relative_end": **********.959527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.95958, "relative_start": 31.976680040359497, "end": **********.95958, "relative_end": **********.95958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.962898, "relative_start": 31.979998111724854, "end": **********.962898, "relative_end": **********.962898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.963153, "relative_start": 31.980252981185913, "end": **********.963153, "relative_end": **********.963153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.963198, "relative_start": 31.980298042297363, "end": **********.963198, "relative_end": **********.963198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.965309, "relative_start": 31.98240900039673, "end": **********.965309, "relative_end": **********.965309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.965557, "relative_start": 31.982657194137573, "end": **********.965557, "relative_end": **********.965557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.965596, "relative_start": 31.982696056365967, "end": **********.965596, "relative_end": **********.965596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.968426, "relative_start": 31.985526084899902, "end": **********.968426, "relative_end": **********.968426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.968825, "relative_start": 31.98592519760132, "end": **********.968825, "relative_end": **********.968825, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.96887, "relative_start": 31.98597002029419, "end": **********.96887, "relative_end": **********.96887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.970934, "relative_start": 31.98803400993347, "end": **********.970934, "relative_end": **********.970934, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.971229, "relative_start": 31.9883291721344, "end": **********.971229, "relative_end": **********.971229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.971267, "relative_start": 31.988367080688477, "end": **********.971267, "relative_end": **********.971267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.97323, "relative_start": 31.99032998085022, "end": **********.97323, "relative_end": **********.97323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.973553, "relative_start": 31.990653038024902, "end": **********.973553, "relative_end": **********.973553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.97363, "relative_start": 31.990730047225952, "end": **********.97363, "relative_end": **********.97363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.975435, "relative_start": 31.99253511428833, "end": **********.975435, "relative_end": **********.975435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.975567, "relative_start": 31.992667198181152, "end": **********.975567, "relative_end": **********.975567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.975642, "relative_start": 31.99274206161499, "end": **********.975642, "relative_end": **********.975642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.977879, "relative_start": 31.9949791431427, "end": **********.977879, "relative_end": **********.977879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.977926, "relative_start": 31.995026111602783, "end": **********.977926, "relative_end": **********.977926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.979811, "relative_start": 31.99691104888916, "end": **********.979811, "relative_end": **********.979811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.984207, "relative_start": 32.001307010650635, "end": **********.984207, "relative_end": **********.984207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.98517, "relative_start": 32.002269983291626, "end": **********.98517, "relative_end": **********.98517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.986047, "relative_start": 32.00314712524414, "end": **********.986047, "relative_end": **********.986047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.986863, "relative_start": 32.003962993621826, "end": **********.986863, "relative_end": **********.986863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.987602, "relative_start": 32.00470209121704, "end": **********.987602, "relative_end": **********.987602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.988327, "relative_start": 32.00542712211609, "end": **********.988327, "relative_end": **********.988327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.989076, "relative_start": 32.00617599487305, "end": **********.989076, "relative_end": **********.989076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.990206, "relative_start": 32.00730609893799, "end": **********.990206, "relative_end": **********.990206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.991824, "relative_start": 32.00892400741577, "end": **********.991824, "relative_end": **********.991824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.993913, "relative_start": 32.01101303100586, "end": **********.993913, "relative_end": **********.993913, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.994459, "relative_start": 32.011559009552, "end": **********.994459, "relative_end": **********.994459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.994519, "relative_start": 32.011619091033936, "end": **********.994519, "relative_end": **********.994519, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.997653, "relative_start": 32.014753103256226, "end": **********.997653, "relative_end": **********.997653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.997718, "relative_start": 32.01481819152832, "end": **********.997718, "relative_end": **********.997718, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Auth\\Access\\Events\\GateEvaluated", "start": **********.002607, "relative_start": 32.01970720291138, "end": **********.002607, "relative_end": **********.002607, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Auth\\Access\\Events\\GateEvaluated"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.003116, "relative_start": 32.02021598815918, "end": **********.003116, "relative_end": **********.003116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.003159, "relative_start": 32.020259141922, "end": **********.003159, "relative_end": **********.003159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.00483, "relative_start": 32.02192997932434, "end": **********.00483, "relative_end": **********.00483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: 2548ffbf4d41f5cc86ea949d4376e84f::icon", "start": **********.004876, "relative_start": 32.02197599411011, "end": **********.004876, "relative_end": **********.004876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: notifications", "start": **********.008942, "relative_start": 32.026041984558105, "end": **********.008942, "relative_end": **********.008942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: notifications", "start": **********.009196, "relative_start": 32.02629613876343, "end": **********.009196, "relative_end": **********.009196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: partials.confetti-js", "start": **********.015766, "relative_start": 32.03286600112915, "end": **********.015766, "relative_end": **********.015766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "composing: partials.confetti-js", "start": **********.01591, "relative_start": 32.**************, "end": **********.01591, "relative_end": **********.01591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Routing\\Events\\ResponsePrepared", "start": **********.206849, "relative_start": 32.**************, "end": **********.206849, "relative_end": **********.206849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\ResponsePrepared"}], "nb_measures": 322}, "views": {"count": 96, "nb_templates": 96, "templates": [{"name": "1x dashboard", "param_count": null, "params": [], "start": **********.705956, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.phpdashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "dashboard"}, {"name": "91x 2548ffbf4d41f5cc86ea949d4376e84f::icon", "param_count": null, "params": [], "start": **********.481223, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Providers/../../resources/views/blade/icon.blade.php2548ffbf4d41f5cc86ea949d4376e84f::icon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fblade%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}, "render_count": 91, "name_original": "2548ffbf4d41f5cc86ea949d4376e84f::icon"}, {"name": "1x partials.bootstrap-table", "param_count": null, "params": [], "start": **********.687342, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/partials/bootstrap-table.blade.phppartials.bootstrap-table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fpartials%2Fbootstrap-table.blade.php&line=1", "ajax": false, "filename": "bootstrap-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.bootstrap-table"}, {"name": "1x layouts.default", "param_count": null, "params": [], "start": 1755333605.115292, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/default.blade.phplayouts.default", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Flayouts%2Fdefault.blade.php&line=1", "ajax": false, "filename": "default.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.default"}, {"name": "1x notifications", "param_count": null, "params": [], "start": **********.009682, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/notifications.blade.phpnotifications", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fnotifications.blade.php&line=1", "ajax": false, "filename": "notifications.blade.php", "line": "?"}, "render_count": 1, "name_original": "notifications"}, {"name": "1x partials.confetti-js", "param_count": null, "params": [], "start": **********.016934, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/partials/confetti-js.blade.phppartials.confetti-js", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fpartials%2Fconfetti-js.blade.php&line=1", "ajax": false, "filename": "confetti-js.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.confetti-js"}]}, "route": {"uri": "GET /", "middleware": "web, auth, breadcrumbs", "controller": "\\App\\Http\\Controllers\\DashboardController@index<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=27\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "home", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=27\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/DashboardController.php:27-53</a>"}, "queries": {"count": 30, "nb_statements": 30, "nb_visible_statements": 30, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.05552, "accumulated_duration_str": "55.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `assets` where `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 37}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 21, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.353043, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:37", "source": {"index": 19, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=37", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "37"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `deployable` = 0 and `pending` = 0 and `archived` = 1 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0, 0, 1], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.366166, "duration": 0.00244, "duration_str": "2.44ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:39", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=39", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "39"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`assigned_to` is null and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `deployable` = 1 and `pending` = 0 and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.3753629, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:47", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=47", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "47"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assigned_to` > '0' and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["0"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.385474, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:54", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=54", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "54"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `deployable` = 0 and `pending` = 0 and `archived` = 1 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0, 0, 1], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 61}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.395545, "duration": 0.0025, "duration_str": "2.5ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:61", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=61", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "61"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `deployable` = 0 and `pending` = 1 and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0, 1, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 68}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.403712, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:68", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=68", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "68"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `deployable` = 0 and `pending` = 0 and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0, 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 75}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.4116979, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:75", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=75", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "75"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `byod` = '1' and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 82}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.418443, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:82", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 82}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=82", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "82"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`next_audit_date` is not null and `assets`.`next_audit_date` between '2025-08-16' and '2025-08-16' and `assets`.`archived` = 0 and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16", "2025-08-16", 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 89}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.426095, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:89", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=89", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "89"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`next_audit_date` is not null and `assets`.`next_audit_date` < '2025-08-16' and `assets`.`archived` = 0 and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16", 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 96}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.434388, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:96", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=96", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "96"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`expected_checkin` is not null and `assets`.`expected_checkin` between '2025-08-16' and '2025-08-16' and `assets`.`archived` = 0 and `assets`.`assigned_to` is not null and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16", "2025-08-16", 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 103}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.442418, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:103", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=103", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "103"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`expected_checkin` is not null and `assets`.`expected_checkin` < '2025-08-16' and `assets`.`archived` = 0 and `assets`.`assigned_to` is not null and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16", 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 110}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 59}], "start": **********.449603, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:110", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=110", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "110"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 33}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.47322, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:33", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=33", "ajax": false, "filename": "DashboardController.php", "line": "33"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `accessories` where `accessories`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 34}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.4804099, "duration": 0.00319, "duration_str": "3.19ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:34", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=34", "ajax": false, "filename": "DashboardController.php", "line": "34"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `license_seats` where `deleted_at` is null and `license_seats`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/License.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\License.php", "line": 452}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.662049, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "License.php:452", "source": {"index": 16, "namespace": null, "name": "app/Models/License.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\License.php", "line": 452}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FModels%2FLicense.php&line=452", "ajax": false, "filename": "License.php", "line": "452"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `consumables` where `consumables`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.672045, "duration": 0.00233, "duration_str": "2.33ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:36", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=36", "ajax": false, "filename": "DashboardController.php", "line": "36"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `components` where `components`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 37}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.68059, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:37", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=37", "ajax": false, "filename": "DashboardController.php", "line": "37"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `users` where `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.68879, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:38", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\DashboardController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=38", "ajax": false, "filename": "DashboardController.php", "line": "38"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 39}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.4675431, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "dashboard:39", "source": {"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fdashboard.blade.php&line=39", "ajax": false, "filename": "dashboard.blade.php", "line": "39"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`expected_checkin` is not null and `assets`.`expected_checkin` < '2025-08-16' and `assets`.`archived` = 0 and `assets`.`assigned_to` is not null and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16", 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 400}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.4939392, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "dashboard:400", "source": {"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 400}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fdashboard.blade.php&line=400", "ajax": false, "filename": "dashboard.blade.php", "line": "400"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `next_audit_date` < '2025-08-16 08:40:04' and `next_audit_date` is not null and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16 08:40:04"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 457}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.504244, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "dashboard:457", "source": {"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 457}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fdashboard.blade.php&line=457", "ajax": false, "filename": "dashboard.blade.php", "line": "457"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `asset_eol_date` is not null and `asset_eol_date` <= '2025-08-16 08:40:04' and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16 08:40:04"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 513}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.5151942, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "dashboard:513", "source": {"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 513}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fdashboard.blade.php&line=513", "ajax": false, "filename": "dashboard.blade.php", "line": "513"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `warranty_months` is not null and DATE_ADD(purchase_date, INTERVAL warranty_months MONTH) < NOW() and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 569}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.524127, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "dashboard:569", "source": {"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 569}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fdashboard.blade.php&line=569", "ajax": false, "filename": "dashboard.blade.php", "line": "569"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `consumables` where `qty` <= `min_amt` and `min_amt` > 0 and `consumables`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 625}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.533109, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "dashboard:625", "source": {"index": 16, "namespace": "view", "name": "dashboard", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/dashboard.blade.php", "line": 625}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fdashboard.blade.php&line=625", "ajax": false, "filename": "dashboard.blade.php", "line": "625"}, "connection": "snipeit", "explain": null}, {"sql": "select `consumables`.*, (select count(*) from `consumables_users` where `consumables`.`id` = `consumables_users`.`consumable_id`) as `consumable_assignments_count` from `consumables` where `min_amt` is not null and `consumables`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 748}, {"index": 16, "namespace": "view", "name": "layouts.default", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/default.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.980268, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "Helper.php:748", "source": {"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 748}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHelpers%2FHelper.php&line=748", "ajax": false, "filename": "Helper.php", "line": "748"}, "connection": "snipeit", "explain": null}, {"sql": "select `accessories`.*, (select count(*) from `accessories_checkout` where `accessories`.`id` = `accessories_checkout`.`accessory_id`) as `checkouts_count` from `accessories` where `min_amt` is not null and `accessories`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 749}, {"index": 16, "namespace": "view", "name": "layouts.default", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/default.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.048371, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "Helper.php:749", "source": {"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 749}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHelpers%2FHelper.php&line=749", "ajax": false, "filename": "Helper.php", "line": "749"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `components` where `min_amt` is not null and `components`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 750}, {"index": 16, "namespace": "view", "name": "layouts.default", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/default.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.055886, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Helper.php:750", "source": {"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 750}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHelpers%2FHelper.php&line=750", "ajax": false, "filename": "Helper.php", "line": "750"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `models` where `min_amt` > 0 and `models`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 751}, {"index": 16, "namespace": "view", "name": "layouts.default", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/default.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.066099, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "Helper.php:751", "source": {"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 751}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHelpers%2FHelper.php&line=751", "ajax": false, "filename": "Helper.php", "line": "751"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `licenses` where `min_amt` > 0 and `licenses`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 752}, {"index": 16, "namespace": "view", "name": "layouts.default", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/default.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.072809, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "Helper.php:752", "source": {"index": 15, "namespace": null, "name": "app/Helpers/Helper.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Helpers\\Helper.php", "line": 752}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHelpers%2FHelper.php&line=752", "ajax": false, "filename": "Helper.php", "line": "752"}, "connection": "snipeit", "explain": null}, {"sql": "select `status_labels`.*, (select count(*) from `assets` where `status_labels`.`id` = `assets`.`status_id` and `assets`.`deleted_at` is null) as `asset_count` from `status_labels` where `show_in_nav` = 1 and `status_labels`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "layouts.default", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/default.blade.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.92276, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "layouts.default:1004", "source": {"index": 15, "namespace": "view", "name": "layouts.default", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/default.blade.php", "line": 1004}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Flayouts%2Fdefault.blade.php&line=1004", "ajax": false, "filename": "default.blade.php", "line": "1004"}, "connection": "snipeit", "explain": null}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "logs": {"count": 0, "messages": []}, "files": {"messages": [{"message": "'\\server.php',", "is_string": true}, {"message": "'\\public\\index.php',", "is_string": true}, {"message": "'\\vendor\\autoload.php',", "is_string": true}, {"message": "'\\vendor\\composer\\autoload_real.php',", "is_string": true}, {"message": "'\\vendor\\composer\\platform_check.php',", "is_string": true}, {"message": "'\\vendor\\composer\\ClassLoader.php',", "is_string": true}, {"message": "'\\vendor\\composer\\autoload_static.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\deprecation-contracts\\function.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-mbstring\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-mbstring\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-normalizer\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-normalizer\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-ctype\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-ctype\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-grapheme\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-grapheme\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\string\\Resources\\functions.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-php83\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-php83\\bootstrap81.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Resources\\functions\\dump.php',", "is_string": true}, {"message": "'\\vendor\\ralouphie\\getallheaders\\src\\getallheaders.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-php80\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-idn\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-idn\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\react\\promise\\src\\functions_include.php',", "is_string": true}, {"message": "'\\vendor\\react\\promise\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\functions_include.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\prompts\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\termwind\\src\\Functions.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\clock\\Resources\\now.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-uuid\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-uuid\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Resources\\functions.php',", "is_string": true}, {"message": "'\\vendor\\ramsey\\uuid\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\fakerphp\\faker\\src\\Faker\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\mtdowling\\jmespath.php\\src\\JmesPath.php',", "is_string": true}, {"message": "'\\vendor\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock\\autoload.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\php-text-template\\src\\Template.php',", "is_string": true}, {"message": "'\\vendor\\phpseclib\\phpseclib\\phpseclib\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\flare-client-php\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-php81\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\aws\\aws-sdk-php\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\ezyang\\htmlpurifier\\library\\HTMLPurifier.composer.php',", "is_string": true}, {"message": "'\\vendor\\phpstan\\phpstan\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php',", "is_string": true}, {"message": "'\\vendor\\psy\\psysh\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\helpers\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\league\\csv\\src\\functions_include.php',", "is_string": true}, {"message": "'\\vendor\\league\\csv\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\mockery\\mockery\\library\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\mockery\\mockery\\library\\Mockery.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\collision\\src\\Adapters\\Phpunit\\Autoload.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Runner\\Version.php',", "is_string": true}, {"message": "'\\vendor\\sebastian\\version\\src\\Version.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\collision\\src\\Adapters\\Phpunit\\Subscribers\\EnsurePrinterIsRegisteredSubscriber.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Event\\Events\\Application\\StartedSubscriber.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Event\\Subscriber.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\autoload.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\autoload-php7.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\src\\Compat.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\namespaced.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\sodium_compat.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\constants.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\php84compat_const.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\php84compat.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\stream-xchacha20.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\ristretto255.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock-phpunit\\autoload.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Rule\\InvocationOrder.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\SelfDescribing.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Invocation.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Interface\\MockObject.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Interface\\Stub.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\InvocationMocker.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\InvocationStubber.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\MethodNameMatch.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\ParametersMatch.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\Stub.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\Identity.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Rule\\MethodName.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\InvocationHandler.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock-phpunit\\classes\\MockDisablerPHPUnit10.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Event\\Events\\Test\\Lifecycle\\FinishedSubscriber.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock-phpunit\\classes\\DefaultArgumentRemoverReturnTypes100.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock-phpunit\\classes\\MockObjectProxyReturnTypes100.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-backup\\src\\Helpers\\functions.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\helpers.php',", "is_string": true}, {"message": "'\\bootstrap\\app.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Container\\Container.php',", "is_string": true}, {"message": "'\\vendor\\psr\\container\\src\\ContainerInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Foundation\\Application.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Foundation\\CachesConfiguration.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Foundation\\CachesRoutes.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-kernel\\HttpKernelInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\EventServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\Context\\ContextServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RoutingServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Http\\Kernel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\InteractsWithTime.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Http\\Kernel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Container\\ContextualAttribute.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Tappable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Routing\\BindingRegistrar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Routing\\Registrar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ReflectsClosures.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Events\\Dispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollectionInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Request.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\Request.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\AcceptHeader.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\AcceptHeaderItem.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\FileBag.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\ParameterBag.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\HeaderBag.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\HeaderUtils.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\InputBag.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\ServerBag.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Concerns\\CanBePrecognitive.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Concerns\\InteractsWithContentTypes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Concerns\\InteractsWithFlashData.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Concerns\\InteractsWithInput.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Dumpable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\InteractsWithData.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Conditionable\\Traits\\Conditionable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Arrayable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Carbon.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Carbon.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Date.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Boundaries.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Comparison.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Converter.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\ToStringFormat.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Creator.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\ObjectInitialisation.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\LocalFactory.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Difference.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Macro.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Mixin.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\MagicParameter.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Modifiers.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Mutability.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Cast.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Options.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\StaticOptions.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Localization.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\StaticLocalization.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Rounding.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\IntervalRounding.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Serialization.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Test.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Timestamp.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Units.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Week.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonTimeZone.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonInterval.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\IntervalStep.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonConverterInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\FactoryImmutable.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\clock\\ClockInterface.php',", "is_string": true}, {"message": "'\\vendor\\psr\\clock\\src\\ClockInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonImmutable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadEnvironmentVariables.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Env.php',", "is_string": true}, {"message": "'\\vendor\\phpoption\\phpoption\\src\\PhpOption\\Option.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\RepositoryBuilder.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\ServerConstAdapter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\AdapterInterface.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\ReaderInterface.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\WriterInterface.php',", "is_string": true}, {"message": "'\\vendor\\phpoption\\phpoption\\src\\PhpOption\\Some.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\EnvConstAdapter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\PutenvAdapter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\MultiReader.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\MultiWriter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\ImmutableWriter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\AdapterRepository.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\RepositoryInterface.php',", "is_string": true}, {"message": "'\\vendor\\phpoption\\phpoption\\src\\PhpOption\\None.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php',", "is_string": true}, {"message": "'\\bootstrap\\cache\\config.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Config\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Config\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\EnvironmentDetector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\AliasLoader.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\CanBeEscapedWhenCastToString.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Enumerable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Jsonable.php',", "is_string": true}, {"message": "'\\bootstrap\\cache\\packages.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php',", "is_string": true}, {"message": "'\\bootstrap\\cache\\services.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\CookieServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasEvents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasGlobalScopes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasRelationships.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasTimestamps.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasUniqueIds.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HidesAttributes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\PreventsCircularRecursion.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\HasCollection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Broadcasting\\HasBroadcastChannel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Queue\\QueueableEntity.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Routing\\UrlRoutable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\SerializableClosure.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Signers\\Hmac.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Contracts\\Signer.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Serializers\\Signed.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Contracts\\Serializable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FoundationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\AggregateServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Testing\\ParallelTestingServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Testing\\Concerns\\TestDatabases.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\DeferrableProvider.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\AbstractCloner.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\ClonerInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\HtmlDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Dumper\\HtmlDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Dumper\\CliDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Dumper\\AbstractDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Dumper\\DataDumperInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\DumperInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Concerns\\ResolvesDumpSource.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\VarCloner.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\HigherOrderTapProxy.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Caster\\ReflectionCaster.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\VarDumper.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Uri.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Htmlable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Responsable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Reflector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\PaginationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\PaginationState.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\Paginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Pagination\\Paginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\CursorPaginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractCursorPaginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Pagination\\CursorPaginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\arietimmerman\\laravel-scim-server\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-dompdf\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\eduardokum\\laravel-mail-auto-embed\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\intervention\\image\\src\\Intervention\\Image\\ImageServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\intervention\\image\\src\\Intervention\\Image\\ImageServiceProviderLaravelRecent.php',", "is_string": true}, {"message": "'\\vendor\\laravel-notification-channels\\google-chat\\src\\GoogleChatServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel-notification-channels\\microsoft-teams\\src\\MicrosoftTeamsServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Notification.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Manager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Notifications\\Dispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Notifications\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\Passport.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\ContextualBindingBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Container\\ContextualBindingBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Auth.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\slack-notification-channel\\src\\SlackChannelServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\ui\\src\\UiServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\LivewireServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\LivewireManager.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\EventBus.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\PersistentMiddleware\\PersistentMiddleware.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\Mechanism.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\FrontendAssets\\FrontendAssets.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendBlade.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\CompileLivewireTags\\CompileLivewireTags.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\RenderComponent.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\DataStore.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Laravel\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\collision\\src\\Adapters\\Laravel\\CollisionServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\phpinsights\\src\\Application\\Adapters\\Laravel\\InsightsServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\phpinsights\\src\\Application\\Injectors\\Repositories.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\termwind\\src\\Laravel\\TermwindServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\osa-eg\\laravel-teams-notification\\src\\LaravelTeamsNotificationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\pragmarx\\google2fa-laravel\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-backup\\src\\BackupServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessAssets.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessBladeComponents.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessCommands.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessConfigs.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessInertia.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessMigrations.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessRoutes.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessServiceProviders.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessTranslations.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessViewComposers.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessViews.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessViewSharedData.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Package.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasAssets.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasBladeComponents.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasCommands.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasConfigs.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasInertia.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasInstallCommand.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasMigrations.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasRoutes.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasServiceProviders.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasTranslations.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasViewComposers.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasViews.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasViewSharedData.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\IgnitionServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\ignition\\src\\Config\\IgnitionConfig.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\ignition\\src\\Config\\FileConfigManager.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\ignition\\src\\Contracts\\ConfigManager.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\error-solutions\\src\\SolutionProviderRepository.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\error-solutions\\src\\Contracts\\SolutionProviderRepository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Log.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\ParsesLogConfiguration.php',", "is_string": true}, {"message": "'\\vendor\\psr\\log\\src\\LoggerInterface.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Level.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-signal-aware-command\\src\\SignalAwareCommandServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\BreadcrumbsServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Route.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CreatesRegularExpressionRouteConstraints.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\FiltersControllerMiddleware.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php',", "is_string": true}, {"message": "'\\vendor\\unicodeveloper\\laravel-password\\src\\DumbPasswordServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\AppServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\AuthServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\AuthServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\EventServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\RouteServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\SettingsServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\ValidationServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\BladeServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\LivewireServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\MacroServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\SamlServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\BreadcrumbsServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\ConnectionResolverInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Renderer\\Listener.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteBinding.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\debugbar-routes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteAction.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteUri.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteGroup.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\console\\Application.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\service-contracts\\ResetInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Console\\Application.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Event.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\routes\\web.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\ui\\src\\AuthRouteMethods.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\AboutCommand.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\console\\Command\\Command.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\console\\Command\\SignalableCommandInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\ConfiguresPrompts.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\HasParameters.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\InteractsWithIO.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\InteractsWithSignals.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\PromptsForMissingInput.php',", "is_string": true}, {"message": "'\\vendor\\composer\\InstalledVersions.php',", "is_string": true}, {"message": "'\\vendor\\composer\\installed.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\console\\Output\\OutputInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Stringable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Blade.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Compiler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesAuthorizations.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesClasses.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesComments.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesComponents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesConditionals.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesEchos.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesErrors.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesFragments.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesHelpers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesIncludes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesInjections.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesJson.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesJs.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesLayouts.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesLoops.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesRawPhp.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesSessions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesStacks.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesStyles.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesTranslations.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesUseStatements.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\CompilerInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\EngineResolver.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\CompileLivewireTags\\LivewireTagPrecompiler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\ComponentHookRegistry.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportWireModelingNestedComponents\\SupportWireModelingNestedComponents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\ComponentHook.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportMultipleRootElementDetection\\SupportMultipleRootElementDetection.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\SupportDisablingBackButtonCache.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportNestedComponentListeners\\SupportNestedComponentListeners.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportMorphAwareIfStatement\\SupportMorphAwareIfStatement.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Livewire.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportAutoInjectedAssets\\SupportAutoInjectedAssets.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportComputed\\SupportLegacyComputedPropertySyntax.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportNestingComponents\\SupportNestingComponents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportScriptsAndAssets\\SupportScriptsAndAssets.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportBladeAttributes\\SupportBladeAttributes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ComponentAttributeBag.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportConsoleCommands\\SupportConsoleCommands.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\View\\View.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Renderable.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportReactiveProps\\SupportReactiveProps.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportFileDownloads\\SupportFileDownloads.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportJsEvaluation\\SupportJsEvaluation.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportQueryString\\SupportQueryString.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportFileUploads\\SupportFileUploads.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportTeleporting\\SupportTeleporting.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportLazyLoading\\SupportLazyLoading.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportFormObjects\\SupportFormObjects.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportAttributes\\SupportAttributes.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination\\SupportPagination.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportValidation\\SupportValidation.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportIsolating\\SupportIsolating.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportRedirects\\SupportRedirects.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportStreaming\\SupportStreaming.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportNavigate\\SupportNavigate.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Vite.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportEntangle\\SupportEntangle.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportLocales\\SupportLocales.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportTesting\\SupportTesting.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\SupportModels.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportEvents\\SupportEvents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportLegacyModels\\SupportLegacyModels.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportWireables\\SupportWireables.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Translator.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation-contracts\\TranslatorInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Translator.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\MessageCatalogue.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\MessageCatalogueInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\MetadataAwareInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\CatalogueMetadataAwareInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\TranslatorBagInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation-contracts\\LocaleAwareInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\lazy\\Carbon\\TranslatorStrongType.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\AbstractTranslator.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\TranslatorStrongTypeInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Loader\\ArrayLoader.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Loader\\LoaderInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\MessageFormatter\\MessageFormatterMapper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Formatter\\MessageFormatterInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\lazy\\Carbon\\MessageFormatter\\MessageFormatterMapperStrongType.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Formatter\\MessageFormatter.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Formatter\\IntlFormatter.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Formatter\\IntlFormatterInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\IdentityTranslator.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation-contracts\\TranslatorTrait.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Lang\\en.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Lang\\en_US.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonPeriod.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\lazy\\Carbon\\UnprotectedDatePeriod.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Date.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\DateFactory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Events\\LocaleUpdated.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-backup\\src\\Notifications\\EventHandler.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-backup\\src\\Listeners\\EncryptBackupArchive.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\ignition-routes.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Http\\Controllers\\HealthCheckController.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Http\\Controllers\\ExecuteSolutionController.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Validation\\ValidatesRequests.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Http\\Controllers\\UpdateConfigController.php',", "is_string": true}, {"message": "'\\app\\Exceptions\\Handler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Debug\\ExceptionHandler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\ReportableHandler.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\DumpRecorder\\DumpRecorder.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\DumpRecorder\\MultiDumpHandler.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\JobRecorder\\JobRecorder.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\LogRecorder\\LogRecorder.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\QueryRecorder\\QueryRecorder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\QueueServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Serializers\\Native.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\QueueManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Queue\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Queue\\Monitor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Validator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Validation\\Factory.php',", "is_string": true}, {"message": "'\\app\\Providers\\SnipeTranslationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Translation\\TranslationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Translation\\FileLoader.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Translation\\Loader.php',", "is_string": true}, {"message": "'\\app\\Services\\SnipeTranslator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Translation\\Translator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\NamespacedItemResolver.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Translation\\Translator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifierInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\PresenceVerifierInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Routing\\UrlGenerator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Optional.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\NullHandler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\Handler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\HandlerInterface.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Logger.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\ResettableInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\Logger.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\LogRecord.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\JsonSerializableDateTimeImmutable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\Context\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\Events\\MessageLogged.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\LogRecorder\\LogMessage.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\URL.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Schema.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ConfigurationUrlParser.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DetectsConcurrencyErrors.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DetectsLostConnections.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\ConnectionInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Grammars\\MySqlGrammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Grammars\\Grammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Grammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\CompilesJsonPaths.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseTransactionsManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Events\\ConnectionEstablished.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Events\\ConnectionEvent.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Grammars\\Grammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php',", "is_string": true}, {"message": "'\\app\\Models\\Asset.php',", "is_string": true}, {"message": "'\\app\\Models\\Depreciable.php',", "is_string": true}, {"message": "'\\app\\Models\\SnipeModel.php',", "is_string": true}, {"message": "'\\app\\Models\\CompanyableTrait.php',", "is_string": true}, {"message": "'\\app\\Models\\Traits\\HasUploads.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Factories\\HasFactory.php',", "is_string": true}, {"message": "'\\app\\Models\\Loggable.php',", "is_string": true}, {"message": "'\\app\\Models\\Requestable.php',", "is_string": true}, {"message": "'\\app\\Presenters\\Presentable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\SoftDeletes.php',", "is_string": true}, {"message": "'\\vendor\\watson\\validating\\src\\ValidatingTrait.php',", "is_string": true}, {"message": "'\\vendor\\watson\\validating\\src\\Injectors\\UniqueInjector.php',", "is_string": true}, {"message": "'\\app\\Http\\Traits\\UniqueUndeletedTrait.php',", "is_string": true}, {"message": "'\\app\\Models\\Traits\\Acceptable.php',", "is_string": true}, {"message": "'\\app\\Models\\Traits\\Searchable.php',", "is_string": true}, {"message": "'\\app\\Models\\CompanyableScope.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Scope.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\SoftDeletingScope.php',", "is_string": true}, {"message": "'\\vendor\\watson\\validating\\src\\ValidatingObserver.php',", "is_string": true}, {"message": "'\\app\\Observers\\AssetObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\User.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Authenticatable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Auth\\Access\\Authorizable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Passwords\\CanResetPassword.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\HasApiTokens.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Notifiable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\HasDatabaseNotifications.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\RoutesNotifications.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Authenticatable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Access\\Authorizable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\CanResetPassword.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Translation\\HasLocalePreference.php',", "is_string": true}, {"message": "'\\app\\Observers\\UserObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\Accessory.php',", "is_string": true}, {"message": "'\\app\\Observers\\AccessoryObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\Component.php',", "is_string": true}, {"message": "'\\app\\Observers\\ComponentObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\Consumable.php',", "is_string": true}, {"message": "'\\app\\Observers\\ConsumableObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\License.php',", "is_string": true}, {"message": "'\\app\\Observers\\LicenseObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\Setting.php',", "is_string": true}, {"message": "'\\app\\Observers\\SettingObserver.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Gate.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\HandlesAuthorization.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Access\\Gate.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Unit.php',", "is_string": true}, {"message": "'\\app\\Listeners\\LogListener.php',", "is_string": true}, {"message": "'\\app\\Listeners\\CheckoutableListener.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\RateLimiter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cache\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\FileStore.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RetrievesMultipleKeys.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cache\\Store.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cache\\LockProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cache\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\psr\\simple-cache\\src\\CacheInterface.php',", "is_string": true}, {"message": "'\\routes\\api.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResourceRegistrar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\PendingResourceRegistration.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Pluralizer.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\InflectorFactory.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Language.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\English\\InflectorFactory.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\GenericLanguageInflectorFactory.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\LanguageInflectorFactory.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\English\\Rules.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Ruleset.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Transformations.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\WordInflector.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\English\\Inflectible.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Transformation.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Pattern.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Patterns.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\English\\Uninflected.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Substitutions.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Substitution.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Word.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Inflector.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\CachedWordInflector.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\RulesetInflector.php',", "is_string": true}, {"message": "'\\routes\\web\\hardware.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Support\\ClosureScope.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Support\\ReflectionClosure.php',", "is_string": true}, {"message": "'\\routes\\web\\models.php',", "is_string": true}, {"message": "'\\routes\\web\\accessories.php',", "is_string": true}, {"message": "'\\routes\\web\\licenses.php',", "is_string": true}, {"message": "'\\routes\\web\\locations.php',", "is_string": true}, {"message": "'\\routes\\web\\consumables.php',", "is_string": true}, {"message": "'\\routes\\web\\fields.php',", "is_string": true}, {"message": "'\\routes\\web\\components.php',", "is_string": true}, {"message": "'\\routes\\web\\users.php',", "is_string": true}, {"message": "'\\routes\\web\\kits.php',", "is_string": true}, {"message": "'\\routes\\web.php',", "is_string": true}, {"message": "'\\app\\Livewire\\Importer.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Component.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Auth\\Access\\AuthorizesRequests.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Concerns\\InteractsWithProperties.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportEvents\\HandlesEvents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportRedirects\\HandlesRedirects.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportStreaming\\HandlesStreaming.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportAttributes\\HandlesAttributes.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportValidation\\HandlesValidation.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportFormObjects\\HandlesFormObjects.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportJsEvaluation\\HandlesJsEvaluation.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\HandlesDisablingBackButtonCache.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\HealthController.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\DashboardController.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\Controller.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bus\\DispatchesJobs.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\GroupsController.php',", "is_string": true}, {"message": "'\\routes\\scim.php',", "is_string": true}, {"message": "'\\vendor\\arietimmerman\\laravel-scim-server\\src\\RouteProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewFinderInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesFragments.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesLayouts.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesLoops.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesStacks.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesTranslations.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\View\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewName.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\App.php',", "is_string": true}, {"message": "'\\resources\\macros\\macros.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\FormFacade.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\HtmlServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\FormBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\Componentable.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\HtmlBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\FileSessionHandler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Session\\Session.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Breadcrumbs.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Manager.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Trail.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Registrar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Pipeline\\Pipeline.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\TrustProxies.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\NoSessionStore.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\Concerns\\ExcludesPaths.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\MaintenanceModeManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\FileBasedMaintenanceMode.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Foundation\\MaintenanceMode.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SymfonySessionDecorator.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\Session\\SessionInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ViewErrorBag.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckForSetup.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsWhereDateClauses.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ExplainsQueries.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Database\\Query\\Builder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Database\\Eloquent\\Builder.php',", "is_string": true}, {"message": "'\\app\\Models\\Company.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectorInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Events\\StatementPrepared.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Events\\QueryExecuted.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\QueryRecorder\\Query.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Collection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\InteractsWithDictionary.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Queue\\QueueableCollection.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckForDebug.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\CreatesUserProviders.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\StatefulGuard.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Guard.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\SupportsBasicAuth.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\UserProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Hashing\\HashServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Hashing\\HashManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Hashing\\Hasher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Timebox.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\CookieJar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cookie\\QueueingFactory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cookie\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Events\\Authenticated.php',", "is_string": true}, {"message": "'\\app\\Models\\Group.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\InteractsWithPivotTable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\JoinClause.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\TrimStrings.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\SecurityHeaders.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\PreventBackHistory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php',", "is_string": true}, {"message": "'\\vendor\\fruitcake\\php-cors\\src\\CorsService.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\IpUtils.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\LaravelDebugbar.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DebugBar.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\Support\\RequestIdGenerator.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\RequestIdGeneratorInterface.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\Storage\\FilesystemStorage.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\Storage\\StorageInterface.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\PhpInfoCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\DataCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\HasDataFormatter.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\HasXdebugLinks.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\DataCollectorInterface.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\Renderable.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\MessagesCollector.php',", "is_string": true}, {"message": "'\\vendor\\psr\\log\\src\\AbstractLogger.php',", "is_string": true}, {"message": "'\\vendor\\psr\\log\\src\\LoggerTrait.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\MessagesAggregateInterface.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\AssetProvider.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\TimeDataCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\DataFormatter.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\DataFormatterInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\error-handler\\ErrorRenderer\\FileLinkFormatter.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\error-handler\\ErrorRenderer\\ErrorRendererInterface.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\MemoryCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\ExceptionsCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\LaravelCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\EventCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataFormatter\\SimpleFormatter.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\ViewCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\RouteCollector.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\StreamHandler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\AbstractProcessingHandler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\AbstractHandler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\ProcessableHandlerTrait.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\FormattableHandlerTrait.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\ProcessableHandlerInterface.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\FormattableHandlerInterface.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Utils.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Formatter\\LineFormatter.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Formatter\\NormalizerFormatter.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Formatter\\FormatterInterface.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\QueryCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\PDO\\PDOCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataFormatter\\QueryFormatter.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\ObjectCountCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\LivewireCollector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\MailServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\Bridge\\Symfony\\SymfonyMailCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\LogsCollector.php',", "is_string": true}, {"message": "'\\vendor\\psr\\log\\src\\LogLevel.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\FilesCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\MultiAuthCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\GateCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\JavascriptRenderer.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\JavascriptRenderer.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Events\\Routing.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\routing\\Route.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\routing\\RouteCompiler.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\routing\\RouteCompilerInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\routing\\CompiledRoute.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\UriValidator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\ValidatorInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\MethodValidator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\SchemeValidator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\HostValidator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteParameterBinder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Events\\RouteMatched.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Contracts\\ControllerDispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\MiddlewareNameResolver.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\SortedMiddleware.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\EncryptCookies.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\VerifyCsrfToken.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckLocale.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckUserIsActivated.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckForTwoFactor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\AssetCountForSidebar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Session\\Middleware\\AuthenticatesSessions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Middleware\\AuthenticatesRequests.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\BreadcrumbsMiddleware.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\Encrypter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Encryption\\Encrypter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Encryption\\StringEncrypter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Encryption\\DecryptException.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\CookieValuePrefix.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php',", "is_string": true}, {"message": "'\\app\\Helpers\\Helper.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\ApiTokenCookieFactory.php',", "is_string": true}, {"message": "'\\app\\Models\\Statuslabel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\BelongsTo.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\ComparesRelatedModels.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\SupportsDefaultModels.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteSignatureParameters.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Support\\ClosureStream.php',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n            $trail->parent('hardware.index')\r\n                ->push(\\trans('general.bulkaudit'), \\route('asset.import-history'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n            $trail->parent('hardware.index')\r\n                ->push('Quickscan Checkin', \\route('hardware/quickscancheckin'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n            $trail->parent('hardware.index')\r\n                ->push(\\trans('admin/hardware/general.requested'), \\route('assets.requested'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n            $trail->parent('hardware.index')\r\n                ->push(\\trans_choice('general.audit_due_days', \\App\\Models\\Setting::getSettings()->audit_warning_days, ['days' => \\App\\Models\\Setting::getSettings()->audit_warning_days]), \\route('assets.audit.due'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n            $trail->parent('hardware.index')\r\n                ->push(\\trans_choice('general.checkin_due_days', \\App\\Models\\Setting::getSettings()->due_checkin_days, ['days' => \\App\\Models\\Setting::getSettings()->due_checkin_days]), \\route('assets.audit.due'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, \\App\\Models\\Asset $asset) =>\r\n            $trail->parent('hardware.show', $asset)\r\n                ->push(\\trans('general.audit'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n                $trail->parent('hardware.index')\r\n                ->push(\\trans('general.import-history'), \\route('asset.import-history'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, \\App\\Models\\Asset $asset) =>\r\n            $trail->parent('hardware.show', $asset)\r\n                ->push(\\trans('admin/hardware/general.checkout'), \\route('hardware.index'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, \\App\\Models\\Asset $asset) =>\r\n        $trail->parent('hardware.show', $asset)\r\n            ->push(\\trans('admin/hardware/general.checkin'), \\route('hardware.index'))\r\n        ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n            $trail->parent('hardware.index')\r\n                ->push(\\trans('admin/hardware/general.bulk_checkout'), \\route('hardware.index'))\r\n            ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('models.index')\r\n            ->push(\\trans('admin/models/table.clone'), \\route('models.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, \\App\\Models\\License $license) =>\r\n        $trail->parent('licenses.show', $license)\r\n            ->push(\\trans('general.checkout'), \\route('licenses.checkout', $license))\r\n        ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, \\App\\Models\\LicenseSeat $licenseSeat) =>\r\n        $trail->parent('licenses.show', $licenseSeat->license)\r\n            ->push(\\trans('general.checkin'), \\route('licenses.checkin', $licenseSeat))\r\n        ',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.backups'), \\route('kits.licenses.edit'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, \\App\\Models\\PredefinedKit $kit) =>\r\n        $trail->parent('kits.show', $kit)\r\n            ->push(\\trans('general.checkout'), \\route('kits.checkout.show', $kit))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.general_title'), \\route('settings.general.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.branding_title'), \\route('settings.branding.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.security_title'), \\route('settings.security.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.localization_title'), \\route('settings.localization.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.alert_title'), \\route('settings.alerts.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.webhook_title'), \\route('settings.slack.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.asset_tag_title'), \\route('settings.asset_tags.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.labels_title'), \\route('settings.labels.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.ldap_ad'), \\route('settings.ldap.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.php_info'), \\route('settings.phpinfo.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.oauth'), \\route('settings.oauth.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.google_login'), \\route('settings.google.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.purge'), \\route('settings.purge.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.login'), \\route('settings.logins.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('settings.index')\r\n            ->push(\\trans('admin/settings/general.saml_title'), \\route('settings.saml.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n            $trail->parent('settings.index')\r\n                ->push(\\trans('admin/settings/general.backups'), \\route('settings.backups.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n    $trail->parent('home')\r\n        ->push(\\trans('general.import'), \\route('imports.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n                $trail->parent('home')\r\n            ->push(\\trans('general.editprofile'), \\route('profile'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.changepassword'), \\route('account.password.index'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.manage_api_keys'), \\route('user.api'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.viewassets'), \\route('view-assets'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.requested_assets_menu'), \\route('account.requested'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.requestable_items'), \\route('requestable-assets'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.accept_assets_menu'), \\route('account.accept'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, $id) =>\r\n        $trail->parent('account.accept')\r\n            ->push(\\trans('general.accept_item'), \\route('account.accept.item', $id))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.audit_report'), \\route('reports.audit'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.depreciation_report'), \\route('reports/depreciation'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.depreciation_report'), \\route('reports.audit'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.asset_maintenance_report'), \\route('reports/asset_maintenances'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.asset_maintenance_report'), \\route('reports/export/asset_maintenances'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.license_report'), \\route('reports/licenses'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.custom_report'), \\route('reports/custom'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, \\App\\Models\\ReportTemplate $reportTemplate) =>\r\n                $trail->parent('reports/custom')\r\n                    ->push($reportTemplate->name, null)\r\n                    ->push(\\trans('general.customize_report'), '')',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail, \\App\\Models\\ReportTemplate $reportTemplate) =>\r\n                $trail->parent('reports/custom')\r\n                    ->push($reportTemplate->name, \\route('report-templates.show', $reportTemplate))\r\n                    ->push(\\trans('general.customize_report'), '')',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.activity_report'), \\route('reports.activity'))',", "is_string": true}, {"message": "'laravel-serializable-closure://fn (\\Tabuna\\Breadcrumbs\\Trail $trail) =>\r\n        $trail->parent('home')\r\n            ->push(\\trans('general.unaccepted_asset_report'), \\route('reports/unaccepted_assets'))',", "is_string": true}, {"message": "'\\app\\Models\\LicenseSeat.php',", "is_string": true}, {"message": "'\\app\\Models\\CompanyableChildTrait.php',", "is_string": true}, {"message": "'\\app\\Models\\ICompanyableChild.php',", "is_string": true}, {"message": "'\\app\\Models\\CompanyableChildScope.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\View\\Engine.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Events\\PreparingResponse.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\Response.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\ResponseHeaderBag.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\ResponseTrait.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\DeterministicBladeKeys.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Drawer\\Regexes.php',", "is_string": true}, {"message": "'\\storage\\framework\\views\\634f41542304d78abb74aff576389081.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\general.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteUrlGenerator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\AnonymousComponent.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ComponentSlot.php',", "is_string": true}, {"message": "'\\storage\\framework\\views\\e42f7a353f1c45b64eeb8c8510b72bd0.php',", "is_string": true}, {"message": "'\\app\\Helpers\\IconHelper.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\DebugBarVarDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\Data.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\Stub.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\VarDumper\\DebugBarHtmlDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\Cursor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Events\\GateEvaluated.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Response.php',", "is_string": true}, {"message": "'\\storage\\framework\\views\\3cc4bcaaf43f1522cca5afa49210928a.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Mix.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\HtmlString.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\table.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\admin\\hardware\\general.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\admin\\hardware\\message.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\button.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\admin\\licenses\\general.php',", "is_string": true}, {"message": "'\\storage\\framework\\views\\603bcce3082fdd3035041e8b49695eb1.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Request.php',", "is_string": true}, {"message": "'\\app\\Models\\ConsumableAssignment.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasMany.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\SupportsInverseRelations.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Expression.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Database\\Query\\Expression.php',", "is_string": true}, {"message": "'\\app\\Models\\AccessoryCheckout.php',", "is_string": true}, {"message": "'\\app\\Models\\AssetModel.php',", "is_string": true}, {"message": "'\\app\\Http\\Traits\\TwoColumnUniqueUndeletedTrait.php',", "is_string": true}, {"message": "'\\app\\Presenters\\UserPresenter.php',", "is_string": true}, {"message": "'\\app\\Presenters\\Presenter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Storage.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Filesystem\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\UnixVisibility\\PortableVisibilityConverter.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\UnixVisibility\\VisibilityConverter.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem-local\\LocalFilesystemAdapter.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\FilesystemAdapter.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\ChecksumProvider.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\PathPrefixer.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem-local\\FallbackMimeTypeDetector.php',", "is_string": true}, {"message": "'\\vendor\\league\\mime-type-detection\\src\\MimeTypeDetector.php',", "is_string": true}, {"message": "'\\vendor\\league\\mime-type-detection\\src\\FinfoMimeTypeDetector.php',", "is_string": true}, {"message": "'\\vendor\\league\\mime-type-detection\\src\\ExtensionLookup.php',", "is_string": true}, {"message": "'\\vendor\\league\\mime-type-detection\\src\\GeneratedExtensionToMimeTypeMap.php',", "is_string": true}, {"message": "'\\vendor\\league\\mime-type-detection\\src\\ExtensionToMimeTypeMap.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\Visibility.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\LocalFilesystemAdapter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Filesystem\\Cloud.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Filesystem\\Filesystem.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\Filesystem.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\CalculateChecksumFromStream.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\FilesystemOperator.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\FilesystemReader.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\FilesystemWriter.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\Config.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\WhitespacePathNormalizer.php',", "is_string": true}, {"message": "'\\vendor\\league\\flysystem\\src\\PathNormalizer.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\admin\\custom_fields\\general.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Crumb.php',", "is_string": true}, {"message": "'\\storage\\framework\\views\\3b1b16c575e20bc87c056cbfccd78ad8.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\MessageBag.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\MessageBag.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\MessageProvider.php',", "is_string": true}, {"message": "'\\storage\\framework\\views\\7c349fc95507b8c23e02248e992c8a67.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\datepicker.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\validation.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Session.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Events\\ResponsePrepared.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\SymfonyHttpDriver.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\HttpDriverInterface.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\SessionCollector.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\uid\\Ulid.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\uid\\AbstractUid.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\uid\\HashableInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\uid\\TimeBasedUidInterface.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\RequestCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\Support\\Clockwork\\ClockworkCollector.php',", "is_string": true}], "count": 1063}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"mgpiattos\"\n  \"user\" => array:43 [\n    \"id\" => 1\n    \"email\" => \"<EMAIL>\"\n    \"activated\" => 1\n    \"created_by\" => null\n    \"activated_at\" => null\n    \"last_login\" => \"2025-08-16 08:39:49\"\n    \"first_name\" => \"<PERSON>\"\n    \"last_name\" => \"Piattos\"\n    \"created_at\" => \"2025-08-16T08:39:28.000000Z\"\n    \"updated_at\" => \"2025-08-16T08:39:49.000000Z\"\n    \"deleted_at\" => null\n    \"website\" => null\n    \"country\" => null\n    \"gravatar\" => null\n    \"location_id\" => null\n    \"phone\" => null\n    \"jobtitle\" => null\n    \"manager_id\" => null\n    \"employee_num\" => null\n    \"avatar\" => null\n    \"username\" => \"mgpiattos\"\n    \"notes\" => null\n    \"company_id\" => null\n    \"ldap_import\" => 0\n    \"locale\" => \"en-US\"\n    \"show_in_list\" => 1\n    \"two_factor_enrolled\" => 0\n    \"two_factor_optin\" => 0\n    \"department_id\" => null\n    \"address\" => null\n    \"city\" => null\n    \"state\" => null\n    \"zip\" => null\n    \"skin\" => null\n    \"remote\" => 0\n    \"start_date\" => null\n    \"end_date\" => null\n    \"scim_externalid\" => null\n    \"autoassign_licenses\" => 1\n    \"vip\" => 0\n    \"enable_sounds\" => 0\n    \"enable_confetti\" => 0\n    \"groups\" => []\n  ]\n]", "api": "null"}, "names": "web: mgpiattos"}, "gate": {"count": 54, "messages": [{"message": "[\n  ability => create,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.549954, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\License,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\License]\n]", "message_html": "<pre class=sf-dump id=sf-dump-780159225 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\License</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\License</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\License]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-780159225\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.682488, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Accessory,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Accessory]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Accessory</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Accessory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Accessory]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.683271, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Consumable,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Consumable]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1065536132 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Consumable</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Consumable</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\Consumable]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1065536132\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.684523, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Component,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Component]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1314044949 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Component</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Component</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Component]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1314044949\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.68509, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1045485513 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1045485513\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.685641, "xdebug_link": null}, {"message": "[\n  ability => index,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>index App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.85782, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\License,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\License]\n]", "message_html": "<pre class=sf-dump id=sf-dump-867934428 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\License</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\License</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\License]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-867934428\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.943252, "xdebug_link": null}, {"message": "[\n  ability => index,\n  target => App\\Models\\Accessory,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Accessory]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>index App\\Models\\Accessory</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Accessory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Accessory]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.945774, "xdebug_link": null}, {"message": "[\n  ability => index,\n  target => App\\Models\\Consumable,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Consumable]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2044295983 data-indent-pad=\"  \"><span class=sf-dump-note>index App\\Models\\Consumable</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Consumable</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\Consumable]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2044295983\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.948066, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Component,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Component]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1622786565 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Component</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Component</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Component]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1622786565\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.949839, "xdebug_link": null}, {"message": "[\n  ability => index,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1082455038 data-indent-pad=\"  \"><span class=sf-dump-note>index App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1082455038\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.951853, "xdebug_link": null}, {"message": "[ability => admin, target => null, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-189008736 data-indent-pad=\"  \"><span class=sf-dump-note>admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-189008736\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.954691, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-757484363 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-757484363\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.955198, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\License,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\License]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\License</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\License</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\License]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.956951, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Accessory,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Accessory]\n]", "message_html": "<pre class=sf-dump id=sf-dump-432722437 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Accessory</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Accessory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Accessory]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-432722437\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.958631, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Consumable,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Consumable]\n]", "message_html": "<pre class=sf-dump id=sf-dump-754808202 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Consumable</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Consumable</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\Consumable]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-754808202\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.960169, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Component,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Component]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1170519929 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Component</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Component</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Component]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1170519929\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.962596, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-163383858 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-163383858\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.964443, "xdebug_link": null}, {"message": "[ability => admin, target => null, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-107243924 data-indent-pad=\"  \"><span class=sf-dump-note>admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-107243924\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.966489, "xdebug_link": null}, {"message": "[\n  ability => superadmin,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2079691289 data-indent-pad=\"  \"><span class=sf-dump-note>superadmin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">superadmin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2079691289\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.083895, "xdebug_link": null}, {"message": "[\n  ability => viewRequestable,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1617435434 data-indent-pad=\"  \"><span class=sf-dump-note>viewRequestable App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">viewRequestable</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1617435434\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.907148, "xdebug_link": null}, {"message": "[\n  ability => self.profile,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1668695470 data-indent-pad=\"  \"><span class=sf-dump-note>self.profile </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">self.profile</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1668695470\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.910287, "xdebug_link": null}, {"message": "[\n  ability => self.api,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1337812104 data-indent-pad=\"  \"><span class=sf-dump-note>self.api </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">self.api</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1337812104\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.912978, "xdebug_link": null}, {"message": "[\n  ability => superadmin,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-936710321 data-indent-pad=\"  \"><span class=sf-dump-note>superadmin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">superadmin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-936710321\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.915008, "xdebug_link": null}, {"message": "[ability => admin, target => null, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-251020262 data-indent-pad=\"  \"><span class=sf-dump-note>admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-251020262\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.917192, "xdebug_link": null}, {"message": "[\n  ability => index,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-714083244 data-indent-pad=\"  \"><span class=sf-dump-note>index App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-714083244\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.918534, "xdebug_link": null}, {"message": "[\n  ability => audit,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1769820192 data-indent-pad=\"  \"><span class=sf-dump-note>audit App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">audit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1769820192\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.948269, "xdebug_link": null}, {"message": "[\n  ability => checkin,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-264602776 data-indent-pad=\"  \"><span class=sf-dump-note>checkin App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">checkin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-264602776\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.950098, "xdebug_link": null}, {"message": "[\n  ability => checkin,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1647187630 data-indent-pad=\"  \"><span class=sf-dump-note>checkin App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">checkin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1647187630\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.95172, "xdebug_link": null}, {"message": "[\n  ability => checkout,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1974249972 data-indent-pad=\"  \"><span class=sf-dump-note>checkout App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">checkout</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1974249972\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.953827, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1631671588 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1631671588\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.954577, "xdebug_link": null}, {"message": "[ability => admin, target => null, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1870434894 data-indent-pad=\"  \"><span class=sf-dump-note>admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1870434894\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.955409, "xdebug_link": null}, {"message": "[\n  ability => audit,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>audit App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">audit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.956067, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\License,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\License]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\License</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\License</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\License]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.95659, "xdebug_link": null}, {"message": "[\n  ability => index,\n  target => App\\Models\\Accessory,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Accessory]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>index App\\Models\\Accessory</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Accessory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Accessory]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.958667, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Consumable,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Consumable]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1803684330 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Consumable</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Consumable</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\Consumable]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1803684330\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.962841, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Component,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Component]\n]", "message_html": "<pre class=sf-dump id=sf-dump-948432546 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Component</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Component</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Component]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-948432546\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.965267, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\PredefinedKit,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PredefinedKit]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1952051641 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\PredefinedKit</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\PredefinedKit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\PredefinedKit]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1952051641\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.96837, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1954588566 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1954588566\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.970893, "xdebug_link": null}, {"message": "[ability => import, target => null, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1682832071 data-indent-pad=\"  \"><span class=sf-dump-note>import </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">import</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1682832071\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.97319, "xdebug_link": null}, {"message": "[\n  ability => backend.interact,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-106049013 data-indent-pad=\"  \"><span class=sf-dump-note>backend.interact </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">backend.interact</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-106049013\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.975396, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\CustomField,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\CustomField]\n]", "message_html": "<pre class=sf-dump id=sf-dump-420845985 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\CustomField</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\CustomField</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\CustomField]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-420845985\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.979769, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Statuslabel,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Statuslabel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1701339338 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Statuslabel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Statuslabel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\Statuslabel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1701339338\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.984159, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\AssetModel,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AssetModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-455163578 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\AssetModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\AssetModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\AssetModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-455163578\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.98513, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Category,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Category]\n]", "message_html": "<pre class=sf-dump id=sf-dump-538194280 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Category</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"19 characters\">App\\Models\\Category</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; App\\Models\\Category]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-538194280\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.986008, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Manufacturer,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Manufacturer]\n]", "message_html": "<pre class=sf-dump id=sf-dump-384671363 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Manufacturer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Manufacturer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\Manufacturer]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-384671363\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.986828, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Supplier,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Supplier]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2109697668 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Supplier</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"19 characters\">App\\Models\\Supplier</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; App\\Models\\Supplier]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2109697668\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.987567, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Department,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Department]\n]", "message_html": "<pre class=sf-dump id=sf-dump-425454770 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Department</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Department</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\Department]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-425454770\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.9883, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Location,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Location]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Location</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"19 characters\">App\\Models\\Location</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; App\\Models\\Location]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.989042, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Company,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Company]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2096607188 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Company</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Company</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Company]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2096607188\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.990156, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Depreciation,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Depreciation]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1936675198 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Depreciation</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\Depreciation</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\Depreciation]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1936675198\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.991772, "xdebug_link": null}, {"message": "[\n  ability => reports.view,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2125132477 data-indent-pad=\"  \"><span class=sf-dump-note>reports.view </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">reports.view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2125132477\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.993862, "xdebug_link": null}, {"message": "[\n  ability => viewRequestable,\n  target => App\\Models\\Asset,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Asset]\n]", "message_html": "<pre class=sf-dump id=sf-dump-160254973 data-indent-pad=\"  \"><span class=sf-dump-note>viewRequestable App\\Models\\Asset</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">viewRequestable</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Asset</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Asset]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-160254973\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.002553, "xdebug_link": null}]}, "session": {"url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/_debugbar/assets/javascript?v=1753138890\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "_token": "XcFb6uBj6sHVSCD8PzSZfpkew7wBekXM9V2bT1eX", "backUrl": "http://127.0.0.1:8000/login", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "success": "You have successfully logged in.", "PHPDEBUGBAR_STACK_DATA": "array:1 [\n  \"01K2S0HYWA7KC1WSPZH5296N0X\" => null\n]", "password_hash_web": "$2y$10$2Y6DCYiWr0S5QSGbJatyfuYNCX1.Ib9MeBtmJ22fHJVGhcCwvfQ3G"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000", "action_name": "home", "controller_action": "\\App\\Http\\Controllers\\DashboardController@index", "uri": "GET /", "controller": "\\App\\Http\\Controllers\\DashboardController@index<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=27\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=27\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/DashboardController.php:27-53</a>", "middleware": "web, auth, breadcrumbs", "duration": "32.26s", "peak_memory": "46MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1309760744 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1309760744\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-482212521 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-482212521\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Microsoft Edge&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1078 characters\">csrftoken=eRZeQ4hlaHT8dltJRqLktclcixjXegkX; snipeit_passport_token=eyJpdiI6IlVzZWVFNUppb2NTU0tvNWRkVFBLRXc9PSIsInZhbHVlIjoiVXZaaXhWbk1uSmt6QmMvVGxidFRWSDBiT1RCdDBtNjZqc0FscStBZGYvaWViZmlxQXUrdkNyaU00OUpMYk5lSWo4V0I5bldUMVNmQnl2K0xaeUU5alFqUVVNMDhFUFZ0RHEyN0pZK0dxRmdObGxEMXV0SFp6dzg0MlMvajhEQVRrcnMrMUFxQjJCZ3pid0tYWGNqSGZMNnhSMHBxU3l0bDYySnhQZXBORE5zN3FWMDQycGlPbFIyTnFydjQ3cURQczhUN2NvV0FWNHQ1Y0I5UXRwM3dlY05HVlRoYnhWQ2VFR1h0YXNGaG1GVEVxRTZSaDQ1MkhYaEFxSWhPQkFQelgwcnNGZlNURGxZbEl3c3VobUNqWWVsZmloclBUNTFoUExCN0tyQWR2VzQ2dGVZazBkbnY5Qk5EQ3hFeFlFb1oiLCJtYWMiOiI4OWRjNTE1ODAzZmUwZmEyYjI1MzVhZTAyZTdlNGE2OTY2Yzk5YjQ3MDIzZjFjNWM1MjdhNWIyYWU5YjBkOTk3IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Imo1U212aFk1Y1JpbDhMTjRBMnJTY0E9PSIsInZhbHVlIjoibW16QXYyekVvb3hQVElmckhZbkZ6V3NWOFU0VzI4MXJqNlNRRlMwVHNHMTRLS1l5ZUNCSFJZYzdhd2pNVXFhS2lUMHZLRHJadmVlY1ZUOG5JMFBoNnhBeEJoU1Nyd2txalQ2YjRTNnMvb1RZZ1NBY1p6TVVrRXR1eStmU1RqTGYiLCJtYWMiOiI1MzU0NzNjN2Q1M2Q5MTNlMTQyN2U3NWUwM2E4ODY2ZjM5YmU0NTFiMGU3ODllYmVlNTViZjYwZmM0MTlkOTJiIiwidGFnIjoiIn0%3D; snipeit_session=Q20TsmwTLpe7V4NLPLzu7UJvMbqL6bJ6wfpUffVy</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>csrftoken</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>snipeit_passport_token</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XcFb6uBj6sHVSCD8PzSZfpkew7wBekXM9V2bT1eX</span>\"\n  \"<span class=sf-dump-key>snipeit_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 16 Aug 2025 08:39:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-clockwork-id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">01K2S0JYE2E8191N1GB7MJ2R19</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-clockwork-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-clockwork-path</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">_debugbar/clockwork/</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-659200541 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"62 characters\">http://127.0.0.1:8000/_debugbar/assets/javascript?v=1753138890</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XcFb6uBj6sHVSCD8PzSZfpkew7wBekXM9V2bT1eX</span>\"\n  \"<span class=sf-dump-key>backUrl</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"32 characters\">You have successfully logged in.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K2S0HYWA7KC1WSPZH5296N0X</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$2Y6DCYiWr0S5QSGbJatyfuYNCX1.Ib9MeBtmJ22fHJVGhcCwvfQ3G</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-659200541\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000", "action_name": "home", "controller_action": "\\App\\Http\\Controllers\\DashboardController@index"}, "badge": null}, "clockwork": {"getData": [], "postData": [], "headers": {"host": ["127.0.0.1:8000"], "connection": ["keep-alive"], "cache-control": ["max-age=0"], "upgrade-insecure-requests": ["1"], "user-agent": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"], "accept": ["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"], "sec-fetch-site": ["same-origin"], "sec-fetch-mode": ["navigate"], "sec-fetch-user": ["?1"], "sec-fetch-dest": ["document"], "sec-ch-ua": ["\"Not;A=Brand\";v=\"99\", \"Microsoft Edge\";v=\"139\", \"Chromium\";v=\"139\""], "sec-ch-ua-mobile": ["?0"], "sec-ch-ua-platform": ["\"Windows\""], "referer": ["http://127.0.0.1:8000/login"], "accept-encoding": ["gzip, deflate, br, zstd"], "accept-language": ["en-US,en;q=0.9"], "cookie": ["csrftoken=eRZeQ4hlaHT8dltJRqLktclcixjXegkX; snipeit_passport_token=eyJpdiI6IlVzZWVFNUppb2NTU0tvNWRkVFBLRXc9PSIsInZhbHVlIjoiVXZaaXhWbk1uSmt6QmMvVGxidFRWSDBiT1RCdDBtNjZqc0FscStBZGYvaWViZmlxQXUrdkNyaU00OUpMYk5lSWo4V0I5bldUMVNmQnl2K0xaeUU5alFqUVVNMDhFUFZ0RHEyN0pZK0dxRmdObGxEMXV0SFp6dzg0MlMvajhEQVRrcnMrMUFxQjJCZ3pid0tYWGNqSGZMNnhSMHBxU3l0bDYySnhQZXBORE5zN3FWMDQycGlPbFIyTnFydjQ3cURQczhUN2NvV0FWNHQ1Y0I5UXRwM3dlY05HVlRoYnhWQ2VFR1h0YXNGaG1GVEVxRTZSaDQ1MkhYaEFxSWhPQkFQelgwcnNGZlNURGxZbEl3c3VobUNqWWVsZmloclBUNTFoUExCN0tyQWR2VzQ2dGVZazBkbnY5Qk5EQ3hFeFlFb1oiLCJtYWMiOiI4OWRjNTE1ODAzZmUwZmEyYjI1MzVhZTAyZTdlNGE2OTY2Yzk5YjQ3MDIzZjFjNWM1MjdhNWIyYWU5YjBkOTk3IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Imo1U212aFk1Y1JpbDhMTjRBMnJTY0E9PSIsInZhbHVlIjoibW16QXYyekVvb3hQVElmckhZbkZ6V3NWOFU0VzI4MXJqNlNRRlMwVHNHMTRLS1l5ZUNCSFJZYzdhd2pNVXFhS2lUMHZLRHJadmVlY1ZUOG5JMFBoNnhBeEJoU1Nyd2txalQ2YjRTNnMvb1RZZ1NBY1p6TVVrRXR1eStmU1RqTGYiLCJtYWMiOiI1MzU0NzNjN2Q1M2Q5MTNlMTQyN2U3NWUwM2E4ODY2ZjM5YmU0NTFiMGU3ODllYmVlNTViZjYwZmM0MTlkOTJiIiwidGFnIjoiIn0%3D; snipeit_session=Q20TsmwTLpe7V4NLPLzu7UJvMbqL6bJ6wfpUffVy"]}, "cookies": {"csrftoken": null, "snipeit_passport_token": null, "XSRF-TOKEN": "XcFb6uBj6sHVSCD8PzSZfpkew7wBekXM9V2bT1eX", "snipeit_session": null}, "uri": "/", "method": "GET", "responseStatus": 200, "sessionData": {"url": [], "_previous": {"url": "http://127.0.0.1:8000/_debugbar/assets/javascript?v=1753138890"}, "_flash": {"old": ["success"], "new": []}, "_token": "XcFb6uBj6sHVSCD8PzSZfpkew7wBekXM9V2bT1eX", "backUrl": "http://127.0.0.1:8000/login", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": 1, "success": "You have successfully logged in.", "PHPDEBUGBAR_STACK_DATA": {"01K2S0HYWA7KC1WSPZH5296N0X": null}, "password_hash_web": "$2y$10$2Y6DCYiWr0S5QSGbJatyfuYNCX1.Ib9MeBtmJ22fHJVGhcCwvfQ3G"}}}